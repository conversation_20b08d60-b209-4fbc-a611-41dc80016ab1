import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserClassificationRepository,
  CustomFieldRepository,
  UserProductRepository,
} from '@modules/business/repositories';
import {
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  CustomFieldInputDto,
} from '../dto';
import { UserClassification } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { MetadataHelper } from '../helpers/metadata.helper';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import {
  generateS3Key,
  CategoryFolderEnum,
} from '@shared/utils/generators/s3-key-generator.util';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';

/**
 * Interface cho image object
 */
interface ImageObject {
  key: string;
  position?: number;
  size?: number;
}

/**
 * Interface cho image operation
 */
interface ImageOperation {
  operation: 'ADD' | 'DELETE';
  key?: string;
  position?: number;
  mimeType?: string;
}

/**
 * Interface cho price object với tất cả các properties có thể có
 */
interface ExtendedPriceDto {
  value?: number;
  listPrice?: number;
  salePrice?: number;
  currency?: string;
  priceDescription?: string;
}

/**
 * Service xử lý logic nghiệp vụ cho phân loại sản phẩm
 */
@Injectable()
export class ClassificationService {
  private readonly logger = new Logger(ClassificationService.name);

  constructor(
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly metadataHelper: MetadataHelper,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo phân loại mới cho sản phẩm
   * @param productId ID của sản phẩm
   * @param createDto DTO chứa thông tin phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã tạo
   */
  @Transactional()
  async create(
    productId: number,
    createDto: CreateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(
        `Tạo phân loại mới cho sản phẩm ${productId}, userId=${userId}`,
      );

      // Xử lý custom fields nếu có (classification custom fields không cần validate với database)
      if (createDto.customFields && createDto.customFields.length > 0) {
        this.logger.log(
          `Xử lý ${createDto.customFields.length} custom fields cho classification`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra loại giá và quyền sở hữu
      const product = await this.userProductRepository.findById(productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền tạo phân loại cho sản phẩm này`,
        );
      }

      // Kiểm tra giá sản phẩm theo loại giá
      this.validateClassificationPrice(
        createDto.price,
        product.typePrice,
        createDto.type,
      );

      // Kiểm tra xem có classification cũ cùng type không để preserve images TRƯỚC khi tạo classification mới
      let existingImages: ImageObject[] = [];
      let existingClassificationsToDelete: unknown[] = [];
      try {
        const existingClassifications =
          await this.userClassificationRepository.find({
            where: { productId, type: createDto.type },
          });

        if (existingClassifications.length > 0) {
          const latestClassification =
            existingClassifications[existingClassifications.length - 1];
          if (
            (
              latestClassification.metadata as unknown as Record<
                string,
                unknown
              >
            )?.images
          ) {
            existingImages = (
              latestClassification.metadata as unknown as Record<
                string,
                unknown
              >
            ).images as ImageObject[];
            this.logger.log(
              `Found ${existingImages.length} existing images from previous classification`,
            );
          }

          // Lưu danh sách classifications cũ để xóa sau khi tạo mới thành công
          existingClassificationsToDelete = existingClassifications;
          this.logger.log(
            `Found ${existingClassifications.length} existing classifications to replace`,
          );
        }
      } catch (error) {
        const errorMessage = (error as Error).message;
        this.logger.warn(
          `Could not fetch existing classifications: ${errorMessage}`,
        );
      }

      // Tạo metadata cho classification (không cần validate với database vì là custom fields tự tạo)
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const metadata = this.metadataHelper.buildClassificationMetadata(
        createDto.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        [], // Chưa có ảnh thực tế, sẽ được cập nhật sau khi có classification ID
      );

      // Validate số lượng mua nếu có
      if (
        createDto.minQuantityPerPurchase !== undefined ||
        createDto.maxQuantityPerPurchase !== undefined
      ) {
        this.validatePurchaseQuantity(
          createDto.minQuantityPerPurchase,
          createDto.maxQuantityPerPurchase,
        );
      }

      // Tạo phân loại mới
      const classification = new UserClassification();
      classification.type = createDto.type;
      classification.description = createDto.description || null;
      classification.price = createDto.price as never;
      classification.productId = productId;
      classification.metadata = metadata as never;
      classification.sku = createDto.sku || null;
      classification.minQuantityPerPurchase =
        createDto.minQuantityPerPurchase || null;
      classification.maxQuantityPerPurchase =
        createDto.maxQuantityPerPurchase || null;

      // Lưu phân loại vào database
      const savedClassification =
        await this.userClassificationRepository.save(classification);

      // Cập nhật metadata với key ảnh nếu có imageOperations hoặc imagesMediaTypes
      let finalImages: ImageObject[] = [...existingImages]; // Bắt đầu với images cũ

      // Cast createDto để access imageOperations
      const createDtoWithOperations = createDto as CreateClassificationDto & {
        imageOperations?: ImageOperation[];
      };

      // Xử lý imageOperations (format mới với operations)
      if (
        createDtoWithOperations.imageOperations &&
        createDtoWithOperations.imageOperations.length > 0
      ) {
        // Xử lý DELETE operations trước
        const deleteOperations = createDtoWithOperations.imageOperations.filter(
          (op) => op.operation === 'DELETE',
        );
        for (const deleteOp of deleteOperations) {
          const deleteOperation = deleteOp;
          if (deleteOperation.key) {
            try {
              // Xóa file trên S3
              await this.s3Service.deleteFile(deleteOperation.key);
              this.logger.log(`Successfully deleted classification image from S3: ${deleteOperation.key}`);
            } catch (error) {
              const errorMessage = (error as Error).message;
              this.logger.warn(`Failed to delete classification image from S3: ${deleteOperation.key}, error: ${errorMessage}`);
              // Tiếp tục xử lý mặc dù xóa S3 thất bại
            }

            // Xóa khỏi finalImages
            finalImages = finalImages.filter(
              (img) => img.key !== deleteOperation.key,
            );
            this.logger.log(`Removed classification image key from finalImages: ${deleteOperation.key}`);
          } else if (deleteOperation.position !== undefined) {
            // Xóa theo position (lấy key trước khi xóa để xóa trên S3)
            const imageToDelete = finalImages.find(img => img.position === deleteOperation.position);
            if (imageToDelete?.key) {
              try {
                // Xóa file trên S3
                await this.s3Service.deleteFile(imageToDelete.key);
                this.logger.log(`Successfully deleted classification image from S3: ${imageToDelete.key}`);
              } catch (error) {
                const errorMessage = (error as Error).message;
                this.logger.warn(`Failed to delete classification image from S3: ${imageToDelete.key}, error: ${errorMessage}`);
              }
            }

            // Xóa khỏi finalImages theo position
            finalImages = finalImages.filter(
              (img) => img.position !== deleteOperation.position,
            );
            this.logger.log(
              `Removed classification image at position ${deleteOperation.position} from finalImages`,
            );
          }
        }

        // Lọc ra các thao tác ADD để tạo key ảnh
        const addOperations = createDtoWithOperations.imageOperations.filter(
          (op) => op.operation === 'ADD',
        );

        if (addOperations.length > 0) {
          // Tạo key ảnh với classification ID thực tế
          const now = Date.now();
          const imageKeys = addOperations.map((_, index) => {
            const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
            return generateS3Key({
              baseFolder: 'business',
              categoryFolder: CategoryFolderEnum.IMAGE,
              fileName: fileName,
              useTimeFolder: true,
            });
          });

          // Tạo thông tin ảnh với key, position dựa trên finalImages
          const images = imageKeys.map((key, index) => ({
            key: key,
            position: finalImages.length + index,
          }));

          // Thêm images mới vào finalImages
          finalImages = [...finalImages, ...images];
        }
      }
      // Xử lý imagesMediaTypes (format cũ - mảng string đơn giản cho API tạo mới)
      else if (
        createDto.imagesMediaTypes &&
        createDto.imagesMediaTypes.length > 0
      ) {
        // Kiểm tra xem imagesMediaTypes là mảng string hay mảng operations
        const firstItem = createDto.imagesMediaTypes[0];

        if (typeof firstItem === 'string') {
          // Format cũ: mảng string đơn giản ["image/jpeg", "image/png"]
          const now = Date.now();
          const imageKeys = createDto.imagesMediaTypes.map(
            (_: unknown, index: number) => {
              const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
              return generateS3Key({
                baseFolder: 'business',
                categoryFolder: CategoryFolderEnum.IMAGE,
                fileName: fileName,
                useTimeFolder: true,
              });
            },
          );

          // Tạo thông tin ảnh với key, position dựa trên finalImages
          const images = imageKeys.map((key, index) => ({
            key: key,
            position: finalImages.length + index,
          }));

          // Thêm images mới vào finalImages
          finalImages = [...finalImages, ...images];
        } else {
          // Format mới: mảng operations (fallback cho tương thích ngược)
          const imageOperations = createDto.imagesMediaTypes as any[];

          // Xử lý DELETE operations trước
          const deleteOperations = imageOperations.filter(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            (op) => op.operation === 'DELETE',
          );
          for (const deleteOp of deleteOperations) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            if (deleteOp.key) {
              try {
                // Xóa file trên S3
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                await this.s3Service.deleteFile(deleteOp.key as string);
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this.logger.log(`Successfully deleted classification image from S3: ${deleteOp.key as string}`);
              } catch (error) {
                const errorMessage = (error as Error).message;
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this.logger.warn(`Failed to delete classification image from S3: ${deleteOp.key as string}, error: ${errorMessage}`);
                // Tiếp tục xử lý mặc dù xóa S3 thất bại
              }

              // Xóa khỏi finalImages
              finalImages = finalImages.filter(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                (img) => img.key !== deleteOp.key,
              );
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              this.logger.log(`Removed classification image key from finalImages: ${deleteOp.key as string}`);
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            } else if (deleteOp.position !== undefined) {
              // Xóa theo position (lấy key trước khi xóa để xóa trên S3)
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              const imageToDelete = finalImages.find(img => img.position === deleteOp.position);
              if (imageToDelete?.key) {
                try {
                  // Xóa file trên S3
                  await this.s3Service.deleteFile(imageToDelete.key);
                  this.logger.log(`Successfully deleted classification image from S3: ${imageToDelete.key}`);
                } catch (error) {
                  const errorMessage = (error as Error).message;
                  this.logger.warn(`Failed to delete classification image from S3: ${imageToDelete.key}, error: ${errorMessage}`);
                }
              }

              // Xóa khỏi finalImages theo position
              finalImages = finalImages.filter(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                (img) => img.position !== deleteOp.position,
              );
              this.logger.log(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Removed classification image at position ${deleteOp.position as number} from finalImages`,
              );
            }
          }

          // Lọc ra các thao tác ADD để tạo key ảnh
          const addOperations = imageOperations.filter(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            (op) => op.operation === 'ADD',
          );

          if (addOperations.length > 0) {
            // Tạo key ảnh với classification ID thực tế
            const now = Date.now();
            const imageKeys = addOperations.map((_, index) => {
              const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
              return generateS3Key({
                baseFolder: 'business',
                categoryFolder: CategoryFolderEnum.IMAGE,
                fileName: fileName,
                useTimeFolder: true,
              });
            });

            // Tạo thông tin ảnh với key, position dựa trên finalImages
            const images = imageKeys.map((key, index) => ({
              key: key,
              position: finalImages.length + index,
            }));

            // Thêm images mới vào finalImages
            finalImages = [...finalImages, ...images];
          }
        }
      }

      // Luôn cập nhật metadata với finalImages (bao gồm cả trường hợp có hoặc không có operations)
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const updatedMetadata = this.metadataHelper.buildClassificationMetadata(
        createDto.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        finalImages, // Sử dụng finalImages sau khi đã xử lý DELETE và ADD
      );

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      savedClassification.metadata = updatedMetadata;
      this.logger.log(
        `Saving classification metadata with ${finalImages.length} images`,
      );
      await this.userClassificationRepository.save(savedClassification);

      // Xóa các classifications cũ sau khi tạo mới thành công
      if (existingClassificationsToDelete.length > 0) {
        for (const oldClassification of existingClassificationsToDelete) {
          const classificationObj = oldClassification as Record<
            string,
            unknown
          >;
          await this.userClassificationRepository.delete(
            classificationObj.id as number,
          );
          this.logger.log(
            `Deleted old classification with ID: ${classificationObj.id as number}`,
          );
        }
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata =
        (savedClassification.metadata as unknown as Record<string, unknown>)
          ?.customFields || [];
      const customFieldsResponse = (customFieldsFromMetadata as unknown[]).map(
        (cf) => {
          const customField = cf as Record<string, unknown>;
          return {
            customFieldId: customField.customFieldId,
            value: customField.value,
          };
        },
      );

      // Đảm bảo price có đầy đủ thông tin
      const price = savedClassification.price as unknown as ExtendedPriceDto;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Tạo upload URLs cho ảnh classification
      const classificationUploadUrls: unknown[] = [];
      const imagesFromMetadataForUpload =
        ((savedClassification.metadata as unknown as Record<string, unknown>)
          ?.images as ImageObject[]) || [];

      // Tìm các ảnh mới được thêm bằng cách so sánh với existing images
      const existingImageKeys = existingImages.map((img) => img.key);
      const newImages = imagesFromMetadataForUpload.filter(
        (img) => !existingImageKeys.includes(img.key),
      );

      if (newImages.length > 0) {
        // Xử lý imageOperations (format mới)
        if (
          createDtoWithOperations.imageOperations &&
          createDtoWithOperations.imageOperations.length > 0
        ) {
          const addOperations = createDtoWithOperations.imageOperations.filter(
            (op) => op.operation === 'ADD',
          );

          for (
            let i = 0;
            i < addOperations.length && i < newImages.length;
            i++
          ) {
            try {
              const addOperation = addOperations[i];
              const mediaType = addOperation.mimeType as ImageTypeEnum;
              const imageKey = newImages[i].key;

              // Tạo presigned URL với key từ metadata
              const url = await this.s3Service.createPresignedWithID(
                imageKey,
                TimeIntervalEnum.FIFTEEN_MINUTES,
                mediaType,
                FileSizeEnum.FIVE_MB,
              );

              this.logger.debug(
                `Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`,
              );

              classificationUploadUrls.push({
                url: url,
                key: imageKey,
                index: newImages[i].position || i,
              });
            } catch (error) {
              const errorMessage = (error as Error).message;
              const errorStack = (error as Error).stack;
              this.logger.error(
                `Lỗi khi tạo presigned URL cho ảnh classification: ${errorMessage}`,
                errorStack,
              );
            }
          }
        }
        // Xử lý imagesMediaTypes (format cũ - mảng string)
        else if (
          createDto.imagesMediaTypes &&
          createDto.imagesMediaTypes.length > 0
        ) {
          const firstItem = createDto.imagesMediaTypes[0];

          if (typeof firstItem === 'string') {
            // Format cũ: mảng string đơn giản
            for (
              let i = 0;
              i < createDto.imagesMediaTypes.length && i < newImages.length;
              i++
            ) {
              try {
                const mediaType = createDto.imagesMediaTypes[
                  i
                ] as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(
                  `Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`,
                );

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i,
                });
              } catch (error) {
                const errorMessage = (error as Error).message;
                const errorStack = (error as Error).stack;
                this.logger.error(
                  `Lỗi khi tạo presigned URL cho ảnh classification: ${errorMessage}`,
                  errorStack,
                );
              }
            }
          } else {
            // Format mới: mảng operations (fallback)

            const imageOperations = createDto.imagesMediaTypes as any[];
            const addOperations = imageOperations.filter(
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              (op) => op.operation === 'ADD',
            );

            for (
              let i = 0;
              i < addOperations.length && i < newImages.length;
              i++
            ) {
              try {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                const addOperation = addOperations[i];
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                const mediaType = addOperation.mimeType as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(
                  `Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`,
                );

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i,
                });
              } catch (error) {
                const errorMessage = (error as Error).message;
                const errorStack = (error as Error).stack;
                this.logger.error(
                  `Lỗi khi tạo presigned URL cho ảnh classification: ${errorMessage}`,
                  errorStack,
                );
              }
            }
          }
        }
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata =
        ((savedClassification.metadata as unknown as Record<string, unknown>)
          ?.images as ImageObject[]) || [];
      const imagesWithUrls = this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      const result: any = {
        id: savedClassification.id,
        type: savedClassification.type,
        description: savedClassification.description,
        price,
        customFields: customFieldsResponse,
        sku: savedClassification.sku,
        minQuantityPerPurchase: savedClassification.minQuantityPerPurchase,
        maxQuantityPerPurchase: savedClassification.maxQuantityPerPurchase,
        // Không trả về imagesMediaTypes và imageOperations trong response
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN (lúc tạo sẽ rỗng)
      };

      // Thêm uploadUrls nếu có
      if (classificationUploadUrls.length > 0) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        result.uploadUrls = {
          classificationId: savedClassification.id,
          imagesUploadUrls: classificationUploadUrls,
        };
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return result;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(`Lỗi khi tạo phân loại: ${errorMessage}`, errorStack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_CREATION_FAILED,
        `Lỗi khi tạo phân loại: ${errorMessage}`,
      );
    }
  }

  /**
   * Cập nhật phân loại
   * @param id ID của phân loại
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification =
        await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(
        classification.productId,
      );
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền cập nhật phân loại này`,
        );
      }

      // Cast updateDto để access các properties
      const updateDtoExtended = updateDto as UpdateClassificationDto & {
        description?: string;
        sku?: string;
      };

      // Cập nhật các trường được cung cấp
      if (updateDto.type !== undefined) {
        classification.type = updateDto.type;
      }
      if (updateDtoExtended.description !== undefined) {
        classification.description = updateDtoExtended.description;
      }
      if (updateDto.price !== undefined) {
        // Kiểm tra giá sản phẩm theo loại giá
        this.validateClassificationPrice(
          updateDto.price,
          product.typePrice,
          classification.type,
        );
        classification.price = updateDto.price as never;
      }
      if (updateDtoExtended.sku !== undefined) {
        classification.sku = updateDtoExtended.sku;
      }
      if (updateDto.minQuantityPerPurchase !== undefined) {
        classification.minQuantityPerPurchase =
          updateDto.minQuantityPerPurchase;
      }
      if (updateDto.maxQuantityPerPurchase !== undefined) {
        classification.maxQuantityPerPurchase =
          updateDto.maxQuantityPerPurchase;
      }

      // Validate số lượng mua nếu có thay đổi
      if (
        updateDto.minQuantityPerPurchase !== undefined ||
        updateDto.maxQuantityPerPurchase !== undefined
      ) {
        const finalMinQuantity =
          updateDto.minQuantityPerPurchase !== undefined
            ? updateDto.minQuantityPerPurchase
            : classification.minQuantityPerPurchase;
        const finalMaxQuantity =
          updateDto.maxQuantityPerPurchase !== undefined
            ? updateDto.maxQuantityPerPurchase
            : classification.maxQuantityPerPurchase;
        this.validatePurchaseQuantity(finalMinQuantity, finalMaxQuantity);
      }

      // Cast updateDto để access imageOperations
      const updateDtoWithOperations = updateDto as UpdateClassificationDto & {
        imageOperations?: ImageOperation[];
      };

      // Xử lý các trường tùy chỉnh và imageOperations/imagesMediaTypes nếu có
      if (
        updateDto.customFields !== undefined ||
        updateDtoWithOperations.imageOperations !== undefined ||
        updateDto.imagesMediaTypes !== undefined
      ) {
        // Lấy metadata hiện tại
        const currentMetadata = classification.metadata || { customFields: [] };

        // Sử dụng customFields từ updateDto nếu có, nếu không giữ nguyên
        const customFields =
          updateDto.customFields !== undefined
            ? updateDto.customFields
            : currentMetadata.customFields || [];

        // Không cần lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

        // Xử lý ảnh: xử lý các thao tác ADD và DELETE
        let images =
          ((currentMetadata as unknown as Record<string, unknown>)
            .images as ImageObject[]) || [];
        const imageOperationsToProcess =
          updateDtoWithOperations.imageOperations || updateDto.imagesMediaTypes;
        if (imageOperationsToProcess !== undefined) {
          // Kiểm tra xem có phải là mảng operations không (chỉ xử lý format mới trong update)
          const firstItem = imageOperationsToProcess[0];
          if (
            firstItem &&
            typeof firstItem === 'object' &&
            'operation' in firstItem
          ) {
            // Format mới: mảng operations
            const operations = imageOperationsToProcess as Array<{
              operation: 'ADD' | 'DELETE';
              position?: number;
              key?: string;
              mimeType?: string;
            }>;

            // Xử lý các thao tác DELETE trước
            const deleteOperations = operations.filter(
              (op) => op.operation === 'DELETE',
            );
            for (const deleteOp of deleteOperations) {
              if (deleteOp.key) {
                try {
                  // Xóa file trên S3
                  await this.s3Service.deleteFile(deleteOp.key);
                  this.logger.log(`Successfully deleted classification image from S3: ${deleteOp.key}`);
                } catch (error) {
                  const errorMessage = (error as Error).message;
                  this.logger.warn(`Failed to delete classification image from S3: ${deleteOp.key}, error: ${errorMessage}`);
                  // Tiếp tục xử lý mặc dù xóa S3 thất bại
                }

                // Xóa khỏi database
                images = images.filter((img) => img.key !== deleteOp.key);
                this.logger.log(`Removed classification image key from database: ${deleteOp.key}`);
              } else if (deleteOp.position !== undefined) {
                // Xóa theo position (lấy key trước khi xóa để xóa trên S3)
                const imageToDelete = images.find(img => img.position === deleteOp.position);
                if (imageToDelete?.key) {
                  try {
                    // Xóa file trên S3
                    await this.s3Service.deleteFile(imageToDelete.key);
                    this.logger.log(`Successfully deleted classification image from S3: ${imageToDelete.key}`);
                  } catch (error) {
                    const errorMessage = (error as Error).message;
                    this.logger.warn(`Failed to delete classification image from S3: ${imageToDelete.key}, error: ${errorMessage}`);
                  }
                }

                // Xóa khỏi database theo position
                images = images.filter(
                  (img) => img.position !== deleteOp.position,
                );
                this.logger.log(`Removed classification image at position ${deleteOp.position} from database`);
              }
            }

            // Xử lý các thao tác ADD
            const addOperations = operations.filter(
              (op) => op.operation === 'ADD',
            );
            if (addOperations.length > 0) {
              const now = Date.now();
              const newImages = addOperations.map((_, index) => {
                const fileName = `classification-${classification.id}-image-${images.length + index}-${now}`;
                return {
                  key: generateS3Key({
                    baseFolder: 'business',
                    categoryFolder: CategoryFolderEnum.IMAGE,
                    fileName: fileName,
                    useTimeFolder: true,
                  }),
                  position: images.length + index,
                };
              });
              images = [...images, ...newImages];
            }
          }
          // Nếu không phải format operations, bỏ qua (format cũ không hỗ trợ trong update)
        }

        // Cập nhật metadata với classification custom fields và ảnh (không lưu imagesMediaTypes)
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const newMetadata = this.metadataHelper.buildClassificationMetadata(
          customFields as never,
          [], // Không lưu imagesMediaTypes vào metadata
          images, // Ảnh mới hoặc ảnh cũ
        );
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        classification.metadata = newMetadata;
      }

      // Lưu phân loại vào database
      const updatedClassification =
        await this.userClassificationRepository.save(classification);

      // Lấy custom fields từ metadata (classification custom fields không có ID)
      const customFieldsFromMetadata =
        ((updatedClassification.metadata as unknown as Record<string, unknown>)
          ?.customFields as unknown[]) || [];
      const customFieldsResponse = customFieldsFromMetadata.map((cf) => {
        const customField = cf as Record<string, unknown>;
        return {
          label: customField.label,
          type: customField.type,
          required: customField.required || false,
          value: customField.value,
        };
      });

      // Đảm bảo price có đầy đủ thông tin
      const price = updatedClassification.price as unknown as ExtendedPriceDto;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Không lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

      // Tạo upload URLs cho ảnh classification nếu có thao tác ADD (sử dụng key từ metadata)
      const classificationUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }> = [];
      const imageOperationsForUploadUpdate =
        updateDtoWithOperations.imageOperations || updateDto.imagesMediaTypes;
      if (
        imageOperationsForUploadUpdate &&
        imageOperationsForUploadUpdate.length > 0
      ) {
        const imagesFromMetadata =
          ((
            updatedClassification.metadata as unknown as Record<string, unknown>
          )?.images as ImageObject[]) || [];

        // Kiểm tra xem có phải là mảng operations không
        const firstItem = imageOperationsForUploadUpdate[0];
        if (
          firstItem &&
          typeof firstItem === 'object' &&
          'operation' in firstItem
        ) {
          const operations = imageOperationsForUploadUpdate as Array<{
            operation: 'ADD' | 'DELETE';
            position?: number;
            key?: string;
            mimeType?: string;
          }>;
          const addOperations = operations.filter(
            (op) => op.operation === 'ADD',
          );

          // Tìm các ảnh mới được thêm (những ảnh có position cao nhất)
          if (addOperations.length > 0) {
            // Sắp xếp images theo position để tìm các ảnh mới
            const sortedImages = [...imagesFromMetadata].sort(
              (a, b) => (b.position || 0) - (a.position || 0),
            );
            const newImages = sortedImages.slice(0, addOperations.length);

            for (
              let i = 0;
              i < addOperations.length && i < newImages.length;
              i++
            ) {
              try {
                const addOperation = addOperations[i];
                const mediaType = addOperation.mimeType as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(
                  `Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`,
                );

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i,
                });
              } catch (error) {
                const errorMessage = (error as Error).message;
                const errorStack = (error as Error).stack;
                this.logger.error(
                  `Lỗi khi tạo presigned URL cho ảnh classification: ${errorMessage}`,
                  errorStack,
                );
              }
            }
          }
        }
        // Nếu không phải format operations, bỏ qua
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata =
        ((updatedClassification.metadata as unknown as Record<string, unknown>)
          ?.images as ImageObject[]) || [];
      const imagesWithUrls = this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        description: updatedClassification.description,
        price,
        customFields: customFieldsResponse as never,
        sku: updatedClassification.sku,
        minQuantityPerPurchase: updatedClassification.minQuantityPerPurchase,
        maxQuantityPerPurchase: updatedClassification.maxQuantityPerPurchase,
        // Không trả về imagesMediaTypes và imageOperations trong response
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
        uploadUrls:
          classificationUploadUrls.length > 0
            ? {
                classificationId: updatedClassification.id,
                imagesUploadUrls: classificationUploadUrls,
              }
            : undefined,
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi cập nhật phân loại: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED,
        `Lỗi khi cập nhật phân loại: ${errorMessage}`,
      );
    }
  }

  /**
   * Xóa phân loại
   * @param id ID của phân loại
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification =
        await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(
        classification.productId,
      );
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền xóa phân loại này`,
        );
      }

      // Xóa ảnh S3 trước khi xóa classification
      await this.deleteClassificationImages(classification);

      // Xóa phân loại
      await this.userClassificationRepository.delete(id);
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(`Lỗi khi xóa phân loại: ${errorMessage}`, errorStack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_DELETION_FAILED,
        `Lỗi khi xóa phân loại: ${errorMessage}`,
      );
    }
  }

  /**
   * Lấy danh sách phân loại theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại
   */
  async getByProductId(
    productId: number,
  ): Promise<ClassificationResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách phân loại cho sản phẩm ${productId}`);

      // Tìm tất cả phân loại của sản phẩm
      const classifications =
        await this.userClassificationRepository.findByProductId_user(productId);

      // Chuyển đổi sang DTO response
      const result: ClassificationResponseDto[] = [];
      for (const classification of classifications) {
        // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
        const customFieldsFromMetadata =
          classification.metadata?.customFields || [];
        const customFields: CustomFieldInputDto[] =
          customFieldsFromMetadata.map((cf) => {
            const customField = cf as unknown as Record<string, unknown>;
            return {
              customFieldId: customField.customFieldId,
              value: customField.value,
            };
          }) as never;

        // Đảm bảo price có đầy đủ thông tin
        const price = classification.price as unknown as ExtendedPriceDto;
        if (price && typeof price === 'object') {
          // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
          if (price.listPrice === undefined && price.value !== undefined) {
            price.listPrice = price.value;
          }
          if (price.salePrice === undefined && price.value !== undefined) {
            price.salePrice = price.value;
          }
          if (price.value === undefined && price.salePrice !== undefined) {
            price.value = price.salePrice;
          }
        }

        // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
        const imagesFromMetadata =
          ((classification.metadata as unknown as Record<string, unknown>)
            ?.images as ImageObject[]) || [];
        const imagesWithUrls = this.generateImageUrls(imagesFromMetadata);

        // Thêm vào kết quả
        result.push({
          id: classification.id,
          type: classification.type,
          description: classification.description,
          price,
          customFields,
          sku: classification.sku,
          minQuantityPerPurchase: classification.minQuantityPerPurchase,
          maxQuantityPerPurchase: classification.maxQuantityPerPurchase,
          imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
        });
      }

      return result;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi lấy danh sách phân loại: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy danh sách phân loại: ${errorMessage}`,
      );
    }
  }

  /**
   * Lấy chi tiết phân loại theo ID
   * @param id ID của phân loại
   * @returns Chi tiết phân loại
   */
  async getById(id: number): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết phân loại với ID ${id}`);

      // Tìm phân loại theo ID
      const classification =
        await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata =
        classification.metadata?.customFields || [];
      const customFields: CustomFieldInputDto[] = customFieldsFromMetadata.map(
        (cf) => {
          const customField = cf as unknown as Record<string, unknown>;
          return {
            customFieldId: customField.customFieldId,
            value: customField.value,
          };
        },
      ) as never;

      // Đảm bảo price có đầy đủ thông tin
      const price = classification.price as unknown as ExtendedPriceDto;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
      const imagesFromMetadata =
        ((classification.metadata as unknown as Record<string, unknown>)
          ?.images as ImageObject[]) || [];
      const imagesWithUrls = this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: classification.id,
        type: classification.type,
        description: classification.description,
        price,
        customFields,
        sku: classification.sku,
        minQuantityPerPurchase: classification.minQuantityPerPurchase,
        maxQuantityPerPurchase: classification.maxQuantityPerPurchase,
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi lấy chi tiết phân loại: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy chi tiết phân loại: ${errorMessage}`,
      );
    }
  }

  /**
   * Kiểm tra giá sản phẩm theo loại giá
   * @param price Giá của phân loại
   * @param typePrice Loại giá của sản phẩm
   * @param classificationType Loại phân loại
   * @throws AppException nếu giá không hợp lệ
   */
  private validateClassificationPrice(
    price: unknown,
    typePrice: PriceTypeEnum,
    classificationType: string,
  ): void {
    const priceObj = price as ExtendedPriceDto;
    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        // Kiểm tra có đủ các trường cần thiết không
        if (
          !priceObj ||
          !priceObj.listPrice ||
          !priceObj.salePrice ||
          !priceObj.currency
        ) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ listPrice, salePrice và currency khi sản phẩm có loại giá HAS_PRICE`,
          );
        }

        // Kiểm tra giá bán phải nhỏ hơn hoặc bằng giá niêm yết
        if (priceObj.salePrice > priceObj.listPrice) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice) trong phân loại "${classificationType}"`,
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        // Kiểm tra có trường priceDescription không
        if (!priceObj || !priceObj.priceDescription) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có priceDescription khi sản phẩm có loại giá STRING_PRICE`,
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        // Kiểm tra price phải là null
        if (price !== null) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" không được có giá khi sản phẩm có loại giá NO_PRICE`,
          );
        }
        break;

      // Các loại giá khác như HOURLY, DAILY, MONTHLY, YEARLY, CONTACT
      default:
        // Kiểm tra có đủ các trường cần thiết không
        if (!priceObj || !priceObj.value || !priceObj.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ value và currency khi sản phẩm có loại giá ${String(typePrice)}`,
          );
        }
        break;
    }
  }

  /**
   * Tạo URL ảnh với CDN cho danh sách ảnh
   * @param images Danh sách ảnh từ metadata
   * @returns Danh sách ảnh với URL CDN
   */

  private generateImageUrls(
    images: Array<{ key: string; size?: number; position?: number }>,
  ): Array<{ key: string; position: number; url: string }> {
    if (!images || images.length === 0) {
      return [];
    }

    return images.map((image) => {
      try {
        // Tạo CDN signed URL
        const url = this.cdnService.generateUrlView(
          image.key,
          TimeIntervalEnum.ONE_HOUR,
        );
        const timestamp = Date.now();

        return {
          key: image.key,
          position: image.position || 0,
          url: url ? `${url}?t=${timestamp}` : '',
        };
      } catch (error) {
        const errorMessage = (error as Error).message;
        const errorStack = (error as Error).stack;
        this.logger.error(
          `Lỗi khi tạo URL cho ảnh classification: ${errorMessage}`,
          errorStack,
        );
        return {
          key: image.key,
          position: image.position || 0,
          url: '',
        };
      }
    });
  }

  /**
   * Cập nhật thông tin ảnh thực tế cho classification sau khi upload
   * @param classificationId ID của classification
   * @param userId ID của user
   * @param images Danh sách ảnh đã upload
   * @returns Classification đã cập nhật
   */
  @Transactional()
  async updateClassificationImages(
    classificationId: number,
    userId: number,
    images: Array<{ key: string; size: number; position: number }>,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(
        `Cập nhật ảnh cho classification ${classificationId}, userId=${userId}`,
      );

      // Kiểm tra classification có tồn tại và thuộc về user không
      const classification = await this.userClassificationRepository
        .createQueryBuilder('classification')
        .leftJoinAndSelect('classification.product', 'product')
        .where('classification.id = :classificationId', { classificationId })
        .andWhere('product.createdBy = :userId', { userId })
        .getOne();

      if (!classification) {
        throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND);
      }

      // Lấy metadata hiện tại

      const currentMetadata = classification.metadata || { customFields: [] };

      // Cập nhật metadata với thông tin ảnh thực tế (không lưu imagesMediaTypes)
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const newMetadata = this.metadataHelper.buildClassificationMetadata(
        (currentMetadata.customFields || []) as never,
        [], // Không lưu imagesMediaTypes vào metadata
        images, // Thông tin ảnh thực tế
      );

      // Cập nhật classification
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      classification.metadata = newMetadata;
      const updatedClassification =
        await this.userClassificationRepository.save(classification);

      this.logger.log(`Đã cập nhật ảnh cho classification ${classificationId}`);

      // Xử lý price
      let price = updatedClassification.price as unknown;
      if (typeof price === 'string') {
        price = JSON.parse(price) as unknown;
      }
      if (typeof price === 'object' && price !== null) {
        const priceObj = price as ExtendedPriceDto;
        if (priceObj.salePrice !== undefined && priceObj.salePrice !== null) {
          priceObj.value = priceObj.salePrice;
        }
      }

      // Xử lý custom fields response
      const customFieldsResponse = currentMetadata.customFields || [];

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata =
        ((updatedClassification.metadata as unknown as Record<string, unknown>)
          ?.images as ImageObject[]) || [];
      const imagesWithUrls = this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        price: price as never,
        customFields: customFieldsResponse as never,
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi cập nhật ảnh classification: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tất cả ảnh S3 của classification
   * @param classification Classification entity
   */
  private async deleteClassificationImages(classification: UserClassification): Promise<void> {
    try {
      // Lấy danh sách ảnh từ metadata
      const imagesFromMetadata =
        ((classification.metadata as unknown as Record<string, unknown>)?.images as ImageObject[]) || [];

      if (imagesFromMetadata.length === 0) {
        this.logger.log(`Classification ${classification.id} không có ảnh để xóa`);
        return;
      }

      this.logger.log(`Xóa ${imagesFromMetadata.length} ảnh S3 cho classification ${classification.id}`);

      // Xóa từng ảnh trên S3
      for (const image of imagesFromMetadata) {
        if (image.key) {
          try {
            await this.s3Service.deleteFile(image.key);
            this.logger.log(`Đã xóa ảnh S3: ${image.key}`);
          } catch (error) {
            const errorMessage = (error as Error).message;
            this.logger.warn(`Không thể xóa ảnh S3: ${image.key}, lỗi: ${errorMessage}`);
            // Tiếp tục xóa các ảnh khác
          }
        }
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      this.logger.error(`Lỗi khi xóa ảnh classification ${classification.id}: ${errorMessage}`);
      // Không throw error để không cản trở việc xóa classification
    }
  }

  /**
   * Validate số lượng mua
   * @param minQuantity Số lượng tối thiểu
   * @param maxQuantity Số lượng tối đa
   * @throws AppException nếu số lượng không hợp lệ
   */
  private validatePurchaseQuantity(
    minQuantity: number | null | undefined,
    maxQuantity: number | null | undefined,
  ): void {
    if (minQuantity !== null && minQuantity !== undefined && minQuantity < 1) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối thiểu mỗi lần mua phải lớn hơn 0',
      );
    }

    if (maxQuantity !== null && maxQuantity !== undefined && maxQuantity < 1) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối đa mỗi lần mua phải lớn hơn 0',
      );
    }

    if (
      minQuantity !== null &&
      minQuantity !== undefined &&
      maxQuantity !== null &&
      maxQuantity !== undefined &&
      minQuantity > maxQuantity
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối thiểu không được lớn hơn số lượng tối đa',
      );
    }
  }
}
