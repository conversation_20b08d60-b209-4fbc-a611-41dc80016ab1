# Fix Inventory Array Response

## 🔍 **Vấn Đ<PERSON> Phát Hiện**

### **API Update Product (PUT):**
```json
"inventory": [
  {"id": "41", "warehouseId": 3, ...},  // Kho Đà Nẵng
  {"id": "42", "warehouseId": 7, ...}   // Kho <PERSON>
]
```
→ **Trả về ARRAY với 2 kho** ✅

### **API Get Detail (GET) - TRƯỚC KHI SỬA:**
```json
"inventory": {
  "id": "41", "warehouseId": 3, ...     // Chỉ có Kho Đà Nẵng
}
```
→ **Tr<PERSON> về OBJECT với 1 kho** ❌

## 🔧 **<PERSON><PERSON><PERSON><PERSON>**

### **1. Logic Lấy Inventory Sai:**
```typescript
// TRƯỚC: Chỉ lấy 1 inventory
const inventoriesResult = await this.inventoryRepository.findAll({ 
  productId: id, 
  limit: 1  // ← Chỉ lấy 1 record
});

// <PERSON><PERSON>h<PERSON> object
productDto['inventory'] = inventoryDto; // Single object
```

### **2. DTO Definition Sai:**
```typescript
// TRƯỚC: Single object
@ApiProperty({
  type: InventoryResponseDto,
})
inventory?: InventoryResponseDto;
```

## ✅ **Đã Sửa**

### **1. Logic Service:**
```typescript
// SAU: Lấy TẤT CẢ inventory
const inventoriesResult = await this.inventoryRepository.findAll({ 
  productId: id  // Không có limit
});

// Xử lý từng inventory
const inventoryDtos: InventoryResponseDto[] = [];
for (const inventory of inventoriesResult.items) {
  // Process each inventory...
  inventoryDtos.push(inventoryDto);
}

// Gán như array
productDto['inventory'] = inventoryDtos; // Array
```

### **2. DTO Definition:**
```typescript
// SAU: Array
@ApiProperty({
  description: 'Thông tin tồn kho sản phẩm ở các kho khác nhau (chỉ có cho sản phẩm PHYSICAL)',
  type: [InventoryResponseDto],
  isArray: true,
  required: false,
})
inventory?: InventoryResponseDto[];
```

## 🧪 **Test Scenario**

### **Setup: Product có 2 inventories**
- Inventory ID 41: Kho Đà Nẵng (warehouseId: 3)
- Inventory ID 42: Kho Hồ Chí Minh (warehouseId: 7)

### **Expected Response - SAU KHI SỬA:**
```json
{
  "code": 200,
  "message": "Lấy chi tiết sản phẩm thành công",
  "result": {
    "id": "344",
    "name": "Quần tây",
    "inventory": [
      {
        "id": "41",
        "productId": "344",
        "warehouseId": 3,
        "warehouse": {
          "name": "Kho Đà Nẵng",
          "description": "Kho phân phối miền Trung",
          "type": "PHYSICAL",
          "address": "Số 789 Đường Nguyễn Tất Thành, Hải Châu, Đà Nẵng",
          "capacity": 3000
        },
        "currentQuantity": 232,
        "availableQuantity": 232,
        "sku": "sssss",
        "barcode": "123234234"
      },
      {
        "id": "42",
        "productId": "344",
        "warehouseId": 7,
        "warehouse": {
          "name": "Kho Hồ Chí Minh",
          "description": "Kho phân phối tại TP.HCM",
          "type": "PHYSICAL",
          "address": "Kho Hồ Chí Minh",
          "capacity": 10000
        },
        "currentQuantity": 23,
        "availableQuantity": 23,
        "sku": "sssss",
        "barcode": "12121312"
      }
    ]
  }
}
```

## 📊 **Expected Logs**

```
[UserProductService] Trả về 2 inventory cho sản phẩm 344
```

## 🎯 **Benefits**

### **1. Consistency:**
- ✅ API Update và API Get Detail đều trả về inventory như **array**
- ✅ Frontend không cần xử lý 2 format khác nhau

### **2. Complete Data:**
- ✅ Hiển thị TẤT CẢ inventory của sản phẩm
- ✅ Không bị mất thông tin kho

### **3. Scalability:**
- ✅ Hỗ trợ sản phẩm có nhiều kho
- ✅ Dễ dàng mở rộng thêm kho mới

## 🚨 **Breaking Change Notice**

### **Frontend Impact:**
```javascript
// TRƯỚC: Single object
const inventory = product.inventory;
const warehouseId = inventory.warehouseId;

// SAU: Array
const inventories = product.inventory;
const firstWarehouse = inventories[0]?.warehouseId;

// Hoặc xử lý tất cả
inventories.forEach(inventory => {
  console.log(`Kho ${inventory.warehouse.name}: ${inventory.availableQuantity}`);
});
```

### **Migration Guide:**
1. **Check if array:** `Array.isArray(product.inventory)`
2. **Handle both formats temporarily:**
```javascript
const inventories = Array.isArray(product.inventory) 
  ? product.inventory 
  : [product.inventory];
```

## 🎉 **Result**

Giờ đây API Get Detail sẽ trả về **TẤT CẢ inventory** như array, nhất quán với API Update! 🚀

### **Test Command:**
```bash
curl -X GET http://localhost:3000/v1/user/products/344 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected:** Trả về array với 2 inventories thay vì 1 object.
