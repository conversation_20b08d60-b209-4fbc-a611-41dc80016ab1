import { Controller, Post, Body, UseGuards, Get, Query, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { EmailMarketingService } from '../services/email-marketing.service';
import { CreateEmailCampaignDto, CreateEmailCampaignWithTemplateDto, CreateEmailCampaignWithTemplateResponseDto, CreateEmailCampaignResponseDto, RecentCampaignsResponseDto, EmailCampaignOverviewResponseDto, RecentCampaignDto, CreateEmailCampaignWithEmailListDto, CreateEmailCampaignWithAudienceListDto } from '../dto/email-campaign';
import { EmailCampaignQueryDto } from '../dto/email-campaign/email-campaign-query.dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { EmailCampaignItemDto } from '../dto/email-campaign/email-campaign-list-response.dto';
import { OverviewDashboardDto, PerformanceMetricsDto, TrendChartDto, TrendsQueryDto, CampaignComparisonDto, CampaignPerformanceListDto } from '../dto/email-campaign/email-reports.dto';
import { QueryDto } from '@common/dto';
import { BulkDeleteEmailCampaignDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';

/**
 * Controller xử lý API liên quan đến email campaign
 */
@ApiTags(SWAGGER_API_TAGS.USER_EMAIL_CAMPAIGN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/email-campaigns')
export class EmailCampaignController {
  constructor(private readonly emailMarketingService: EmailMarketingService) {}

  /**
   * Tạo email campaign và đẩy jobs vào queue
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo email campaign',
    description: 'Tạo chiến dịch email marketing mới, lưu vào database và tạo jobs đẩy vào queue để worker xử lý'
  })
  @ApiResponse({
    status: 201,
    description: 'Email campaign đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc không tìm thấy audience'
  })
  @ApiResponse({
    status: 404,
    description: 'Segment hoặc audience không tồn tại'
  })
  async createEmailCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createEmailCampaignDto: CreateEmailCampaignDto,
  ): Promise<AppApiResponse<CreateEmailCampaignResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaign(user.id, createEmailCampaignDto);
    return wrapResponse(
      result,
      `Email campaign đã được tạo thành công với ${result.jobCount} jobs. Campaign sẽ được xử lý bởi worker.`
    );
  }

  /**
   * Tạo email campaign với template và đẩy jobs vào queue
   */
  @Post('with-template')
  @ApiOperation({
    summary: 'Tạo email campaign với template',
    description: 'Tạo chiến dịch email marketing mới sử dụng template có sẵn, lưu vào database và tạo jobs đẩy vào queue để worker xử lý'
  })
  @ApiResponse({
    status: 201,
    description: 'Email campaign với template đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateEmailCampaignWithTemplateResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc thời gian gửi không hợp lệ'
  })
  @ApiResponse({
    status: 404,
    description: 'Template email, segment hoặc email server configuration không tồn tại'
  })
  async createEmailCampaignWithTemplate(
    @CurrentUser() user: JwtPayload,
    @Body() createEmailCampaignWithTemplateDto: CreateEmailCampaignWithTemplateDto,
  ): Promise<AppApiResponse<CreateEmailCampaignWithTemplateResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaignWithTemplate(user.id, createEmailCampaignWithTemplateDto);
    return wrapResponse(
      result,
      `Email campaign với template đã được tạo thành công. Campaign sẽ được xử lý bởi worker.`
    );
  }

  /**
   * Tạo email campaign với danh sách email cụ thể
   */
  @Post('with-email-list')
  @ApiOperation({
    summary: 'Tạo email campaign với danh sách email',
    description: 'Tạo chiến dịch email marketing mới với danh sách email cụ thể, lưu vào database và tạo jobs đẩy vào queue để worker xử lý'
  })
  @ApiResponse({
    status: 201,
    description: 'Email campaign với danh sách email đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc danh sách email không hợp lệ'
  })
  async createEmailCampaignWithEmailList(
    @CurrentUser() user: JwtPayload,
    @Body() createEmailCampaignWithEmailListDto: CreateEmailCampaignWithEmailListDto,
  ): Promise<AppApiResponse<CreateEmailCampaignResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaignWithEmailList(user.id, createEmailCampaignWithEmailListDto);
    return wrapResponse(
      result,
      `Email campaign với danh sách email đã được tạo thành công với ${result.jobCount} jobs. Campaign sẽ được xử lý bởi worker.`
    );
  }

  /**
   * Tạo email campaign với danh sách audience ID cụ thể
   */
  @Post('with-audience-list')
  @ApiOperation({
    summary: 'Tạo email campaign với danh sách audience ID',
    description: 'Tạo chiến dịch email marketing mới với danh sách audience ID cụ thể, lưu vào database và tạo jobs đẩy vào queue để worker xử lý'
  })
  @ApiResponse({
    status: 201,
    description: 'Email campaign với danh sách audience ID đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc không tìm thấy audience'
  })
  @ApiResponse({
    status: 404,
    description: 'Một số audience không tồn tại'
  })
  async createEmailCampaignWithAudienceList(
    @CurrentUser() user: JwtPayload,
    @Body() createEmailCampaignWithAudienceListDto: CreateEmailCampaignWithAudienceListDto,
  ): Promise<AppApiResponse<CreateEmailCampaignResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaignWithAudienceList(user.id, createEmailCampaignWithAudienceListDto);
    return wrapResponse(
      result,
      `Email campaign với danh sách audience đã được tạo thành công với ${result.jobCount} jobs. Campaign sẽ được xử lý bởi worker.`
    );
  }

  /**
   * Kiểm tra trạng thái email marketing queue
   */
  @Get('queue/status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái queue',
    description: 'Lấy thông tin trạng thái hiện tại của email marketing queue'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trạng thái queue',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Trạng thái queue' },
        data: {
          type: 'object',
          properties: {
            waiting: { type: 'number', example: 10, description: 'Số job đang chờ xử lý' },
            active: { type: 'number', example: 2, description: 'Số job đang được xử lý' },
            completed: { type: 'number', example: 100, description: 'Số job đã hoàn thành' },
            failed: { type: 'number', example: 5, description: 'Số job thất bại' },
          },
        },
      },
    },
  })
  async getQueueStatus(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<any>> {
    const result = await this.emailMarketingService.getQueueStatus();
    return wrapResponse(result, 'Trạng thái email marketing queue');
  }

  /**
   * Lấy danh sách chiến dịch email có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch email',
    description: 'Lấy danh sách các chiến dịch email với phân trang, filter và thống kê chi tiết'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chiến dịch email với phân trang',
    schema: AppApiResponse.getPaginatedSchema(EmailCampaignItemDto)
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: EmailCampaignQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<EmailCampaignItemDto>>> {
    const result = await this.emailMarketingService.getCampaigns(user.id, queryDto);
    return wrapResponse(result, 'Danh sách chiến dịch email');
  }

  /**
   * API Overview Dashboard - Tổng quan thống kê email marketing
   */
  @Get('reports/overview')
  @ApiOperation({
    summary: 'Tổng quan thống kê email marketing',
    description: 'Trả về tổng quan thống kê toàn bộ email marketing của user: tổng số email đã gửi, đã mở, click, hủy đăng ký'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê tổng quan email marketing',
    type: OverviewDashboardDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getOverviewDashboard(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<OverviewDashboardDto>> {
    const result = await this.emailMarketingService.getOverviewDashboard(user.id);
    return wrapResponse(result, 'Thống kê tổng quan email marketing');
  }

  /**
   * API Performance Metrics - Các tỷ lệ hiệu suất
   */
  @Get('reports/performance')
  @ApiOperation({
    summary: 'Các tỷ lệ hiệu suất email marketing',
    description: 'Trả về các tỷ lệ hiệu suất: tỷ lệ mở, click, bounce, hủy đăng ký'
  })
  @ApiResponse({
    status: 200,
    description: 'Các tỷ lệ hiệu suất email marketing',
    type: PerformanceMetricsDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getPerformanceMetrics(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<PerformanceMetricsDto>> {
    const result = await this.emailMarketingService.getPerformanceMetrics(user.id);
    return wrapResponse(result, 'Các tỷ lệ hiệu suất email marketing');
  }

  /**
   * API Trend Chart - Xu hướng theo thời gian
   */
  @Get('reports/trends')
  @ApiOperation({
    summary: 'Xu hướng email marketing theo thời gian',
    description: 'Trả về xu hướng theo thời gian với data points cho biểu đồ: số lượng đã gửi, mở, click theo ngày'
  })
  @ApiResponse({
    status: 200,
    description: 'Xu hướng email marketing theo thời gian',
    type: TrendChartDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getTrendChart(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: TrendsQueryDto,
  ): Promise<AppApiResponse<TrendChartDto>> {
    const result = await this.emailMarketingService.getTrendChart(user.id, queryDto);
    return wrapResponse(result, 'Xu hướng email marketing theo thời gian');
  }

  /**
   * API Campaign Comparison - So sánh các chiến dịch
   */
  @Get('reports/comparison')
  @ApiOperation({
    summary: 'So sánh các chiến dịch email',
    description: 'So sánh các chiến dịch với chỉ số: tên chiến dịch, số lượng đã gửi, mở, click'
  })
  @ApiResponse({
    status: 200,
    description: 'So sánh các chiến dịch email',
    type: CampaignComparisonDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getCampaignComparison(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<CampaignComparisonDto>> {
    const result = await this.emailMarketingService.getCampaignComparison(user.id);
    return wrapResponse(result, 'So sánh các chiến dịch email');
  }

  /**
   * API Campaign Performance List - Danh sách hiệu quả từng chiến dịch
   */
  @Get('reports/campaigns')
  @ApiOperation({
    summary: 'Danh sách hiệu quả từng chiến dịch email',
    description: 'Danh sách có phân trang về hiệu quả từng chiến dịch: tên, số người nhận, tỷ lệ mở/click, trạng thái'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách hiệu quả từng chiến dịch email',
    type: CampaignPerformanceListDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getCampaignPerformanceList(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryDto,
  ): Promise<AppApiResponse<CampaignPerformanceListDto>> {
    const result = await this.emailMarketingService.getCampaignPerformanceList(user.id, queryDto);
    return wrapResponse(result, 'Danh sách hiệu quả từng chiến dịch email');
  }

  /**
   * Lấy danh sách chiến dịch email gần đây
   */
  @Get('recent')
  @ApiOperation({
    summary: 'Lấy chiến dịch email gần đây',
    description: 'Lấy danh sách các chiến dịch email gần đây với thông tin tổng số người nhận, trạng thái, thời gian chạy và tỷ lệ gửi/click'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chiến dịch email gần đây',
    type: RecentCampaignsResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getRecentCampaigns(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<RecentCampaignsResponseDto>> {
    const result = await this.emailMarketingService.getRecentCampaigns(user.id);
    return wrapResponse(result, 'Danh sách chiến dịch email gần đây');
  }

  /**
   * Lấy thống kê tổng quan email campaign
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan email campaign',
    description: 'Lấy thống kê tổng số chiến dịch, đang gửi, đã gửi, đã lên lịch'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê tổng quan email campaign',
    type: EmailCampaignOverviewResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getOverview(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<EmailCampaignOverviewResponseDto>> {
    const result = await this.emailMarketingService.getOverview(user.id);
    return wrapResponse(result, 'Thống kê tổng quan email campaign');
  }

  /**
   * Xóa nhiều email campaign
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều email campaign',
    description: 'Xóa nhiều chiến dịch email marketing. Tự động hủy job trong queue cho campaign đang chạy (SENDING) hoặc đã lên lịch (SCHEDULED).'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa email campaign thành công',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số email campaign không thể xóa',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async bulkDeleteEmailCampaigns(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteEmailCampaignDto
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.emailMarketingService.bulkDeleteEmailCampaigns(user.id, bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }
}
