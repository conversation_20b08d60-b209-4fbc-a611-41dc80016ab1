import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

// TypeScript interfaces tương ứng với Java DTOs
export interface AccessTokenRequest {
  grant_type: string;
  client_id: string;
  client_secret: string;
  scope: string;
  session_id: string;
}

export interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  error?: number;
  error_description?: string;
}

export interface OtpRequest {
  access_token: string;
  session_id: string;
  BrandName: string;
  Phone: string;
  Message: string;
}

export interface OtpResponse {
  MessageId: string;
  BrandName: string;
  Phone: string;
  Message: string;
  PartnerId: string;
  Telco: string;
}

export interface CampaignRequest {
  access_token: string;
  session_id: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string; // Format: "yyyy-MM-dd HH:mm"
  Quota: number;
}

export interface CampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
}

export interface AdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
  PhoneList: string; // Danh sách số điện thoại phân cách bằng dấu phẩy
}

export interface AdsResponse {
  CampaignCode: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  Details: Array<{
    Phone: string;
    Status: string;
    MessageId?: string;
    ErrorCode?: string;
    ErrorMessage?: string;
  }>;
}

export interface DetailsCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface DetailsCampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  CreatedAt: string;
  UpdatedAt: string;
}

export interface DetailsStatusCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsResponse {
  CampaignCode: string;
  Status: string;
  Message: string;
}

export interface DlrAdsRecheckRequest {
  access_token: string;
  session_id: string;
  FromDate: string; // Format: "yyyy-MM-dd"
  ToDate: string;   // Format: "yyyy-MM-dd"
}

export interface DlrAdsRecheckResponse {
  Campaigns: Array<{
    CampaignCode: string;
    CampaignName: string;
    TotalSent: number;
    ReceivedDlr: number;
    PendingDlr: number;
  }>;
}

export interface FprSmsConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  brandName: string;
  sessionId?: string;
}

@Injectable()
export class FprSmsBrandnameService {
  private readonly logger = new Logger(FprSmsBrandnameService.name);
  private accessToken: string | null = null;
  private tokenExpiresAt: Date | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly config: FprSmsConfig
  ) {}

  /**
   * Tạo session ID ngẫu nhiên
   */
  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Kiểm tra và làm mới access token nếu cần
   */
  private async ensureValidToken(): Promise<string> {
    if (!this.accessToken || !this.tokenExpiresAt ||
        new Date() >= this.tokenExpiresAt) {
      await this.getAccessToken();
    }
    return this.accessToken!;
  }

  /**
   * API Lấy Access Token
   */
  async getAccessToken(): Promise<AccessTokenResponse> {
    try {
      const request: AccessTokenRequest = {
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: 'send_brandname_otp send_brandname_ads',
        session_id: this.config.sessionId || this.generateSessionId()
      };

      const response = await firstValueFrom(
        this.httpService.post<AccessTokenResponse>(
          `${this.config.apiUrl}/oauth2/token`,
          request,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      if (response.data.error) {
        throw new Error(`Token error: ${response.data.error_description}`);
      }

      // Lưu token và thời gian hết hạn
      this.accessToken = response.data.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (response.data.expires_in * 1000));

      this.logger.log('Access token retrieved successfully');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get access token', error);
      throw error;
    }
  }

  /**
   * API Gửi Tin Nhắn Brandname OTP
   */
  async sendOtp(request: Omit<OtpRequest, 'access_token' | 'session_id'>): Promise<OtpResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const otpRequest: OtpRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<OtpResponse>(
          `${this.config.apiUrl}/api/push-brandname-otp`,
          otpRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`OTP sent successfully to ${request.Phone}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to send OTP', error);
      throw error;
    }
  }

  /**
   * API Tạo Campaign Quảng Cáo
   */
  async createCampaign(request: Omit<CampaignRequest, 'access_token' | 'session_id'>): Promise<CampaignResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const campaignRequest: CampaignRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<CampaignResponse>(
          `${this.config.apiUrl}/api/create-campaign`,
          campaignRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign created successfully: ${response.data.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to create campaign', error);
      throw error;
    }
  }

  /**
   * API Xem Chi Tiết Campaign Quảng Cáo
   */
  async detailsCampaign(request: Omit<DetailsCampaignRequest, 'access_token' | 'session_id'>): Promise<DetailsCampaignResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const detailsRequest: DetailsCampaignRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<DetailsCampaignResponse>(
          `${this.config.apiUrl}/api/detail-ads`,
          detailsRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign details retrieved: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get campaign details', error);
      throw error;
    }
  }

  /**
   * API Lấy Trạng Thái Của Từng Tin Quảng Cáo
   */
  async detailsStatusCampaign(request: Omit<DetailsStatusCampaignRequest, 'access_token' | 'session_id'>): Promise<ArrayBuffer> {
    try {
      const accessToken = await this.ensureValidToken();

      const statusRequest: DetailsStatusCampaignRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<ArrayBuffer>(
          `${this.config.apiUrl}/api/dlr-ads`,
          statusRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            },
            responseType: 'arraybuffer'
          }
        )
      );

      this.logger.log(`Campaign status details retrieved: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get campaign status details', error);
      throw error;
    }
  }

  /**
   * API Gửi Tin Nhắn Quảng Cáo
   */
  async sendAds(request: Omit<AdsRequest, 'access_token' | 'session_id'>): Promise<AdsResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const adsRequest: AdsRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<AdsResponse>(
          `${this.config.apiUrl}/api/push-brandname-ads`,
          adsRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Ads sent successfully for campaign: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to send ads', error);
      throw error;
    }
  }

  /**
   * API Hủy Tin Nhắn Quảng Cáo
   */
  async cancelAds(request: Omit<CancelAdsRequest, 'access_token' | 'session_id'>): Promise<CancelAdsResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const cancelRequest: CancelAdsRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<CancelAdsResponse>(
          `${this.config.apiUrl}/api/cancel-ads`,
          cancelRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign cancelled successfully: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to cancel ads', error);
      throw error;
    }
  }

  /**
   * API Lấy danh sách campaign QC chưa nhận đủ DLR
   */
  async getDlrAdsRecheck(request: Omit<DlrAdsRecheckRequest, 'access_token' | 'session_id'>): Promise<DlrAdsRecheckResponse> {
    try {
      const accessToken = await this.ensureValidToken();

      const recheckRequest: DlrAdsRecheckRequest = {
        access_token: accessToken,
        session_id: this.config.sessionId || this.generateSessionId(),
        ...request
      };

      const response = await firstValueFrom(
        this.httpService.post<DlrAdsRecheckResponse>(
          `${this.config.apiUrl}/dlr-ads-recheck`,
          recheckRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`DLR recheck data retrieved for period: ${request.FromDate} to ${request.ToDate}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get DLR recheck data', error);
      throw error;
    }
  }

  /**
   * Utility method: Gửi OTP với template mặc định
   */
  async sendOtpWithTemplate(phone: string, otpCode: string, template?: string): Promise<OtpResponse> {
    const message = template
      ? template.replace('{code}', otpCode)
      : `Mã xác thực của bạn là: ${otpCode}. Vui lòng không chia sẻ mã này với ai khác.`;

    return this.sendOtp({
      BrandName: this.config.brandName,
      Phone: phone,
      Message: message
    });
  }

  /**
   * Utility method: Tạo và gửi campaign trong một lần
   */
  async createAndSendCampaign(
    campaignName: string,
    message: string,
    phoneList: string[],
    scheduleTime?: string,
    quota?: number
  ): Promise<{ campaign: CampaignResponse; ads: AdsResponse }> {
    try {
      // Tạo campaign
      const campaign = await this.createCampaign({
        CampaignName: campaignName,
        BrandName: this.config.brandName,
        Message: message,
        ScheduleTime: scheduleTime || new Date().toISOString().slice(0, 16).replace('T', ' '),
        Quota: quota || phoneList.length
      });

      // Gửi ads
      const ads = await this.sendAds({
        CampaignCode: campaign.CampaignCode,
        PhoneList: phoneList.join(',')
      });

      return { campaign, ads };
    } catch (error) {
      this.logger.error('Failed to create and send campaign', error);
      throw error;
    }
  }

  /**
   * Utility method: Kiểm tra trạng thái kết nối
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getAccessToken();
      return true;
    } catch (error) {
      this.logger.error('Connection test failed', error);
      return false;
    }
  }
}