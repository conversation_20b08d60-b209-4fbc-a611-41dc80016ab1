# Architecture: Tập trung tất cả Classification Operations

## Nguyên tắc

**TẤT CẢ classification operations (ADD/UPDATE/DELETE) được xử lý tại MỘT ĐIỂM DUY NHẤT**
**CHỈ XÓA classification và ảnh khi có `operation: "DELETE"`**

## Architecture mới

### BƯỚC 7-8: processAllClassificationOperations
- ✅ Xử lý **TẤT CẢ** classification operations
- ✅ DELETE operations trước
- ✅ ADD/UPDATE operations sau
- ✅ Return `{ classifications, classificationUploadUrls }`

## Flow chi tiết

```typescript
// BƯỚC 7-8: UpdateProductProcessor.processAllClassificationOperations
async processAllClassificationOperations(productId, updateDto, userId) {
  // BƯỚC 1: Xử lý DELETE operations
  await this.processClassificationDeleteOperations(productId, updateDto, userId);
  
  // BƯỚC 2: Xử lý ADD/UPDATE operations  
  const { classifications, classificationUploadUrls } = 
    await this.processClassificationAddUpdateOperations(productId, updateDto, userId);
  
  return { classifications, classificationUploadUrls };
}
```

## Classification Operations

### 1. DELETE Operations
```typescript
// CHỈ xử lý khi có operation: "DELETE"
const deleteOperations = updateDto.classifications.filter(cls => cls.operation === 'DELETE');

if (deleteOperations.length === 0) {
  return; // Không có gì để xóa
}

// 🗑️ XÓA CLASSIFICATIONS theo yêu cầu
await this.processClassificationsDeletion(productId, idsToDelete, userId);
```

### 2. ADD/UPDATE Operations
```typescript
// Lọc chỉ lấy ADD/UPDATE operations
const addUpdateOperations = updateDto.classifications.filter(
  cls => cls.operation !== 'DELETE'
);

// Gọi logic xử lý classifications hiện có
return await this.processClassificationsUpdate(productId, { ...updateDto, classifications: addUpdateOperations }, userId);
```

## Image Operations trong Classifications

### Logic hiện có đã đúng:
```typescript
// Trong processClassificationsUpdate
const classificationDtoWithOperations = classificationDto as UpdateClassificationDto & {
  imageOperations?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;
};

if (classificationDtoWithOperations.imageOperations) {
  // Chỉ xóa ảnh khi có operation: "DELETE"
  // Chỉ thêm ảnh khi có operation: "ADD"
}
```

## Request Examples

### 1. Thêm classification mới với ảnh
```json
{
  "classifications": [
    {
      "operation": "ADD",
      "type": "Size",
      "description": "Large",
      "price": {"amount": 100000, "currency": "VND"},
      "imageOperations": [
        {
          "operation": "ADD",
          "mimeType": "image/jpeg"
        }
      ]
    }
  ]
}
```

### 2. Cập nhật classification và xóa ảnh
```json
{
  "classifications": [
    {
      "operation": "UPDATE",
      "id": 123,
      "description": "Extra Large",
      "imageOperations": [
        {
          "operation": "DELETE",
          "key": "business/IMAGE/old-image.jpg"
        }
      ]
    }
  ]
}
```

### 3. Xóa classification
```json
{
  "classifications": [
    {
      "operation": "DELETE",
      "id": 123
    }
  ]
}
```

### 4. Thao tác phức hợp
```json
{
  "classifications": [
    {
      "operation": "DELETE",
      "id": 123
    },
    {
      "operation": "ADD",
      "type": "Color",
      "description": "Red",
      "imageOperations": [
        {
          "operation": "ADD",
          "mimeType": "image/png"
        }
      ]
    },
    {
      "operation": "UPDATE",
      "id": 124,
      "description": "Blue Updated",
      "imageOperations": [
        {
          "operation": "DELETE",
          "key": "old-blue.jpg"
        },
        {
          "operation": "ADD",
          "mimeType": "image/jpeg"
        }
      ]
    }
  ]
}
```

## Debug Logs

```
🔄 BẮT ĐẦU xử lý TẤT CẢ classification operations cho sản phẩm 347
🗑️ Xử lý 1 DELETE operations cho classifications: 123
➕✏️ Xử lý 2 ADD/UPDATE operations cho classifications
➕ Tạo classification mới với operation: ADD
✏️ Cập nhật classification hiện có với ID: 124
✅ HOÀN THÀNH xử lý TẤT CẢ classification operations cho sản phẩm 347, tạo/cập nhật 2 classifications
```

## Ưu điểm

### ✅ Consistency với Inventory
- Cùng pattern với inventory operations
- DELETE → ADD/UPDATE (thứ tự đúng)
- Centralized logic

### ✅ Image Operations
- Chỉ xóa ảnh khi có `operation: "DELETE"`
- Chỉ thêm ảnh khi có `operation: "ADD"`
- Không có logic tự động đoán

### ✅ Separation of Concerns
- DELETE operations: `processClassificationDeleteOperations`
- ADD/UPDATE operations: `processClassificationAddUpdateOperations`
- Image operations: trong `processClassificationsUpdate`

### ✅ Transaction Safety
- Sử dụng `@Transactional()` để đảm bảo tính nhất quán
- Rollback nếu có error

## Kết quả

- ✅ **Không có logic trùng lặp**
- ✅ **Classification operations tập trung ở một nơi**
- ✅ **Chỉ xóa khi có operation: "DELETE"**
- ✅ **Image operations an toàn**
- ✅ **Thứ tự xử lý đúng: DELETE → ADD/UPDATE**
- ✅ **Dễ debug và maintain**
