-- Debug: <PERSON><PERSON><PERSON> tra inventory có tồn tại trong database không

-- 1. <PERSON><PERSON>m tra inventory cho sản phẩm 348
SELECT * FROM inventory WHERE product_id = 348;

-- 2. <PERSON><PERSON><PERSON> tra tất cả inventory gần đây
SELECT * FROM inventory 
WHERE created_at > (UNIX_TIMESTAMP() - 3600) * 1000  -- 1 giờ gần đây
ORDER BY created_at DESC;

-- 3. Kiểm tra inventory với ID cụ thể từ response
SELECT * FROM inventory WHERE id IN (67, 68);

-- 4. <PERSON><PERSON><PERSON> tra logs nếu có bảng audit
SELECT * FROM audit_logs 
WHERE table_name = 'inventory' 
AND record_id IN (67, 68)
ORDER BY created_at DESC;

-- 5. <PERSON><PERSON><PERSON> tra sản phẩm 348
SELECT id, name, product_type, status FROM user_products WHERE id = 348;
