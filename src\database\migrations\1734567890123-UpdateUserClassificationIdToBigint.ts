import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserClassificationIdToBigint1734567890123 implements MigrationInterface {
  name = 'UpdateUserClassificationIdToBigint1734567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Backup existing data if needed
    await queryRunner.query(`
      -- Create backup table
      CREATE TABLE user_classifications_backup AS 
      SELECT * FROM user_classifications;
    `);

    // Drop foreign key constraints if any
    await queryRunner.query(`
      -- Drop any foreign key constraints that reference user_classifications.id
      -- Add specific constraint drops here if they exist
    `);

    // Change the ID column type from integer to bigint
    await queryRunner.query(`
      ALTER TABLE user_classifications 
      ALTER COLUMN id TYPE BIGINT;
    `);

    // Update the sequence to handle bigint values
    await queryRunner.query(`
      -- Update sequence to handle larger values
      ALTER SEQUENCE user_classifications_id_seq AS BIGINT;
    `);

    // Recreate any foreign key constraints if they existed
    await queryRunner.query(`
      -- Recreate foreign key constraints here if they existed
      -- Example:
      -- ALTER TABLE some_table ADD CONSTRAINT fk_classification_id 
      -- FOREIGN KEY (classification_id) REFERENCES user_classifications(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the changes
    await queryRunner.query(`
      -- Check if any IDs are too large for integer
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM user_classifications WHERE id > 2147483647) THEN
          RAISE EXCEPTION 'Cannot downgrade to integer: some IDs are too large';
        END IF;
      END $$;
    `);

    // Change back to integer
    await queryRunner.query(`
      ALTER TABLE user_classifications 
      ALTER COLUMN id TYPE INTEGER;
    `);

    // Update sequence back to integer
    await queryRunner.query(`
      ALTER SEQUENCE user_classifications_id_seq AS INTEGER;
    `);

    // Drop backup table
    await queryRunner.query(`
      DROP TABLE IF EXISTS user_classifications_backup;
    `);
  }
}
