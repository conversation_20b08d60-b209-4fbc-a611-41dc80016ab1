# Test Inventory Operations

## 🎯 **Vấn Đề Đã Phát Hiện**

### **API Update Response:**
```json
"inventory": [{"id": "48", "warehouseId": 3, ...}]
```
→ **Có inventory** ✅

### **API Get Detail Response:**
```json
"inventory": []  // hoặc không có
```
→ **Không có inventory** ❌

## 🔧 **Nguyên <PERSON> & Giải Pháp**

### **1. Debug API Get Detail:**
- ✅ Thêm debug logs vào `UserProductService.getProductDetail()`
- ✅ Kiểm tra `inventoryRepository.findAll({ productId })` có trả về data không

### **2. Thêm Inventory Operations:**
- ✅ Thêm field `operation: 'ADD' | 'UPDATE'` vào `ProductInventoryDto`
- ✅ Cập nhật logic xử lý trong `PhysicalProductUpdateProcessor`

## 🧪 **Test Inventory Operations**

### **Test Case 1: Thêm inventory mới**

**Request:**
```json
{
  "name": "Test Product",
  "inventory": [
    {
      "operation": "ADD",
      "warehouseId": 3,
      "availableQuantity": 100,
      "sku": "NEW-SKU-001",
      "barcode": "NEW-BARCODE-001"
    }
  ]
}
```

**Expected:**
- ✅ Tạo inventory mới (bỏ qua inventoryId nếu có)
- ✅ Log: "Tạo inventory mới với operation: ADD"

### **Test Case 2: Cập nhật inventory hiện có**

**Request:**
```json
{
  "name": "Test Product",
  "inventory": [
    {
      "inventoryId": 48,
      "operation": "UPDATE",
      "availableQuantity": 150,
      "sku": "UPDATED-SKU"
    }
  ]
}
```

**Expected:**
- ✅ Cập nhật inventory ID 48
- ✅ Log: "Cập nhật inventory hiện có với inventoryId: 48"

### **Test Case 3: Logic cũ (Backward Compatible)**

**Request:**
```json
{
  "name": "Test Product",
  "inventory": [
    {
      "inventoryId": 48,
      "availableQuantity": 200
    },
    {
      "warehouseId": 7,
      "availableQuantity": 50,
      "sku": "NEW-SKU"
    }
  ]
}
```

**Expected:**
- ✅ Inventory có inventoryId → Update
- ✅ Inventory không có inventoryId → Create

### **Test Case 4: Duplicate inventory (operation = ADD)**

**Request:**
```json
{
  "name": "Test Product",
  "inventory": [
    {
      "inventoryId": 48,
      "operation": "ADD",
      "warehouseId": 5,
      "availableQuantity": 75,
      "sku": "DUPLICATE-SKU"
    }
  ]
}
```

**Expected:**
- ✅ Tạo inventory mới (inventoryId 48 bị bỏ qua)
- ✅ Duplicate inventory với warehouse khác

## 🔍 **Debug Steps**

### **1. Test API Get Detail:**
```bash
curl -X GET http://localhost:3000/v1/user/products/345 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Check logs:**
```
[UserProductService] DEBUG: Lấy inventory cho sản phẩm PHYSICAL ID: 345
[UserProductService] DEBUG: Tìm thấy X inventory cho sản phẩm 345
[UserProductService] Trả về X inventory cho sản phẩm 345
```

### **2. Test Database Direct:**
```sql
SELECT * FROM inventory WHERE product_id = 345;
```

### **3. Test Inventory Operations:**
```bash
curl -X PUT http://localhost:3000/v1/user/products/345 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Inventory Operations",
    "inventory": [
      {
        "operation": "ADD",
        "warehouseId": 8,
        "availableQuantity": 25,
        "sku": "TEST-SKU"
      }
    ]
  }'
```

## ✅ **Expected Logs**

```
[UserProductService] DEBUG: Lấy inventory cho sản phẩm PHYSICAL ID: 345
[UserProductService] DEBUG: Tìm thấy 1 inventory cho sản phẩm 345
[PhysicalProductUpdateProcessor] Tạo inventory mới với operation: ADD, inventoryId: undefined
[UserProductService] Trả về 1 inventory cho sản phẩm 345
```

## 🎯 **Benefits của Inventory Operations**

### **1. Explicit Control:**
```javascript
// Frontend có thể chỉ định chính xác
{
  operation: "ADD",     // Tạo mới
  warehouseId: 8,
  availableQuantity: 100
}

{
  inventoryId: 48,
  operation: "UPDATE",  // Cập nhật
  availableQuantity: 150
}
```

### **2. Duplicate Feature:**
```javascript
// Duplicate inventory sang warehouse khác
{
  inventoryId: 48,      // ID gốc
  operation: "ADD",     // Tạo mới
  warehouseId: 9,       // Warehouse khác
  availableQuantity: 50
}
```

### **3. Backward Compatible:**
```javascript
// Code cũ vẫn hoạt động
{
  inventoryId: 48,      // Có ID → Update
  availableQuantity: 200
}

{
  warehouseId: 7,       // Không ID → Create
  availableQuantity: 50
}
```

## 🚨 **Error Cases**

### **Case 1: UPDATE không có inventoryId**
```json
{
  "operation": "UPDATE",
  "warehouseId": 3
}
```
**Expected Error:** Inventory service sẽ báo lỗi khi update với inventoryId undefined

### **Case 2: Invalid Operation**
```json
{
  "operation": "INVALID",
  "warehouseId": 3
}
```
**Expected Error:** Validation error - Operation chỉ chấp nhận ADD hoặc UPDATE

## 🔧 **Implementation Status**

- ✅ Added `operation` field to `ProductInventoryDto`
- ✅ Updated validation with `@IsIn(['ADD', 'UPDATE'])`
- ✅ Updated logic in `PhysicalProductUpdateProcessor.updateSingleInventory()`
- ✅ Added debug logs to `UserProductService.getProductDetail()`
- ✅ Backward compatibility maintained

## 🎉 **Next Steps**

1. **Test API Get Detail** với debug logs
2. **Test Inventory Operations** với các scenarios trên
3. **Kiểm tra database** xem inventory có được lưu đúng không
4. **Fix vấn đề** nếu API Get Detail không trả về inventory
