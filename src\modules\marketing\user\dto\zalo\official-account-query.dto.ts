import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái Official Account
 */
export enum OfficialAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách Official Account có phân trang
 */
export class OfficialAccountQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên Official Account',
    example: 'RedAI',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái kết nối',
    enum: OfficialAccountStatus,
    example: OfficialAccountStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(OfficialAccountStatus)
  status?: OfficialAccountStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
