import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thống kê tổng quan template Zalo
 */
export class TemplateOverviewResponseDto {
  @ApiProperty({
    description: 'Tổng số templates',
    example: 25,
  })
  totalTemplates: number;

  @ApiProperty({
    description: 'Số templates đã được duyệt',
    example: 18,
  })
  approvedTemplates: number;

  @ApiProperty({
    description: 'Số templates đang chờ duyệt',
    example: 5,
  })
  pendingTemplates: number;

  @ApiProperty({
    description: 'Số templates bị từ chối',
    example: 2,
  })
  rejectedTemplates: number;

  @ApiProperty({
    description: 'Chi phí trung bình mỗi tin nhắn (VND)',
    example: 1500,
  })
  averageCostPerMessage: number;

  @ApiProperty({
    description: 'Số templates mới được tạo trong 3 ngày gần đây',
    example: 3,
  })
  newTemplatesLast3Days: number;

  @ApiProperty({
    description: 'Tổng số tin nhắn ZNS đã gửi',
    example: 1250,
  })
  totalMessagesSent: number;

  @ApiProperty({
    description: 'Tổng chi phí đã sử dụng (VND)',
    example: 1875000,
  })
  totalCostSpent: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật thống kê (Unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;
}
