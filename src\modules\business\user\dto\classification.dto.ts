import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
  Allow,
  Min,
  IsIn,
} from 'class-validator';
import { CustomFieldInputDto } from './custom-field-metadata.dto';

/**
 * DTO cho thao tác xử lý hình ảnh trong classification
 */
export class ClassificationImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với hình ảnh',
    enum: ['ADD', 'DELETE'],
    example: 'ADD',
  })
  @IsString()
  @IsIn(['ADD', 'DELETE'], { message: 'Operation chỉ chấp nhận giá trị ADD hoặc DELETE' })
  operation: 'ADD' | 'DELETE';

  @ApiProperty({
    description: 'Key của hình ảnh cần xóa (bắt buộc khi operation = DELETE)',
    example: 'business/IMAGE/2025/06/classification-123-image-0-1234567890.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  key?: string;

  @ApiProperty({
    description: 'MIME type của hình ảnh cần thêm (bắt buộc khi operation = ADD)',
    enum: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    example: 'image/jpeg',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['image/jpeg', 'image/png', 'image/gif', 'image/webp'], {
    message: 'MIME type nên là image/jpeg, image/png, image/gif, image/webp theo enum có sẵn'
  })
  mimeType?: string;

  @ApiProperty({
    description: 'Vị trí của hình ảnh (tùy chọn, hệ thống sẽ tự động gán nếu không có)',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  position?: number;
}

/**
 * DTO cho giá của phân loại khi typePrice là HAS_PRICE
 */
export class ClassificationPriceDto {
  @ApiProperty({
    description: 'Giá niêm yết',
    example: 200000,
  })
  @IsNumber()
  @IsNotEmpty()
  listPrice: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 150000,
  })
  @IsNumber()
  @IsNotEmpty()
  salePrice: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;
}

/**
 * DTO cho giá của phân loại khi typePrice là STRING_PRICE
 */
export class ClassificationStringPriceDto {
  @ApiProperty({
    description: 'Mô tả giá của phân loại',
    example: 'Giá theo màu sắc - liên hệ',
  })
  @IsString()
  @IsNotEmpty()
  priceDescription: string;
}

/**
 * DTO cho tạo phân loại sản phẩm
 */
export class CreateClassificationDto {
  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Mô tả',
    example: 'Mô tả',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Giá của phân loại ( hỗ trợ cả HAS_PRICE và STRING_PRICE)',
    oneOf: [
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' },
      { type: 'null' },
    ],
    examples: {
      hasPriceExample: {
        summary: 'Giá cố định (HAS_PRICE)',
        value: {
          listPrice: 200000,
          salePrice: 150000,
          currency: 'VND',
        },
      },
      stringPriceExample: {
        summary: 'Giá mô tả (STRING_PRICE)',
        value: {
          priceDescription: 'Giá theo màu sắc - liên hệ',
        },
      },
      noPriceExample: {
        summary: 'Không có giá (NO_PRICE)',
        value: null,
      },
    },
    required: false,
  })
  @IsOptional()
  @Allow()
  price?: ClassificationPriceDto | ClassificationStringPriceDto | null;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description:
      'Danh sách ảnh phân loại - hỗ trợ cả format cũ (mảng string) và format mới (mảng operations)',
    oneOf: [
      {
        type: 'array',
        items: { type: 'string' },
        description: 'Format cũ: mảng string MIME types cho API tạo mới',
        example: ['image/jpeg', 'image/png'],
      },
      {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['ADD', 'DELETE'],
              example: 'ADD',
            },
            position: {
              type: 'number',
              example: 1,
              description: 'Vị trí ảnh cần xóa (cho DELETE)',
            },
            key: {
              type: 'string',
              example: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
              description: 'Khóa tệp (cho DELETE)',
            },
            mimeType: {
              type: 'string',
              example: 'image/jpeg',
              description: 'Loại MIME (cho ADD)',
            },
          },
        },
        description: 'Format mới: mảng operations cho API cập nhật',
        example: [
          {
            operation: 'ADD',
            mimeType: 'image/jpeg',
          },
          {
            operation: 'DELETE',
            key: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
          },
        ],
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  imagesMediaTypes?:
    | string[]
    | Array<{
        operation: 'ADD' | 'DELETE';
        position?: number;
        key?: string;
        mimeType?: string;
      }>;

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'SKU-001',
    required: false,
  })
  @IsOptional()
  @IsString()
  sku: string;

  @ApiProperty({
    description: 'Số lượng có sẵn của phân loại',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  availableQuantity?: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase?: number;

  // Service package specific fields
  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút) - cho service packages',
    example: 60,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration?: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp) - cho service packages',
    example: 1704067200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp) - cho service packages',
    example: 1704153600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  endTime?: number;

  @ApiProperty({
    description: 'Múi giờ - cho service packages',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({
    description: 'Trạng thái - cho service packages',
    example: 'PENDING',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'Số lượng có sẵn - cho service packages',
    example: 50,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  quantity?: number;
}

/**
 * DTO cho cập nhật phân loại sản phẩm
 */
export class UpdateClassificationDto {
  @ApiProperty({
    description: 'ID của phân loại (optional - nếu không có sẽ tạo mới)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Loại thao tác với classification',
    enum: ['ADD', 'UPDATE', 'DELETE'],
    example: 'UPDATE',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['ADD', 'UPDATE', 'DELETE'], { message: 'Operation chỉ chấp nhận giá trị ADD, UPDATE hoặc DELETE' })
  operation?: 'ADD' | 'UPDATE' | 'DELETE';

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'SKU-001',
    required: false,
  })
  @IsOptional()
  @IsString()
  sku: string;

  @ApiProperty({
    description: 'Mô tả',
    example: 'Mô tả',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Giá của phân loại (hỗ trợ cả HAS_PRICE và STRING_PRICE)',
    oneOf: [
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' },
      { type: 'null' },
    ],
    examples: {
      hasPriceExample: {
        summary: 'Giá cố định (HAS_PRICE)',
        value: {
          listPrice: 200000,
          salePrice: 150000,
          currency: 'VND',
        },
      },
      stringPriceExample: {
        summary: 'Giá mô tả (STRING_PRICE)',
        value: {
          priceDescription: 'Giá theo màu sắc - liên hệ',
        },
      },
      noPriceExample: {
        summary: 'Không có giá (NO_PRICE)',
        value: null,
      },
    },
    required: false,
  })
  @IsOptional()
  @Allow()
  price?: ClassificationPriceDto | ClassificationStringPriceDto | null;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description:
      'Danh sách ảnh phân loại - hỗ trợ cả format cũ (mảng string) và format mới (mảng operations)',
    oneOf: [
      {
        type: 'array',
        items: { type: 'string' },
        description: 'Format cũ: mảng string MIME types cho API tạo mới',
        example: ['image/jpeg', 'image/png'],
      },
      {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['ADD', 'DELETE'],
              example: 'ADD',
            },
            position: {
              type: 'number',
              example: 1,
              description: 'Vị trí ảnh cần xóa (cho DELETE)',
            },
            key: {
              type: 'string',
              example: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
              description: 'Khóa tệp (cho DELETE)',
            },
            mimeType: {
              type: 'string',
              example: 'image/jpeg',
              description: 'Loại MIME (cho ADD)',
            },
          },
        },
        description: 'Format mới: mảng operations cho API cập nhật',
        example: [
          {
            operation: 'ADD',
            mimeType: 'image/jpeg',
          },
          {
            operation: 'DELETE',
            key: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
          },
        ],
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  imagesMediaTypes?:
    | string[]
    | Array<{
        operation: 'ADD' | 'DELETE';
        position?: number;
        key?: string;
        mimeType?: string;
      }>;

  @ApiProperty({
    description:
      'Danh sách thao tác ảnh phân loại (tên field thay thế cho imagesMediaTypes để nhất quán với API chính)',
    type: [ClassificationImageOperationDto],
    example: [
      {
        operation: 'ADD',
        mimeType: 'image/jpeg',
      },
      {
        operation: 'DELETE',
        key: 'business/IMAGE/2025/06/classification-123-image-0-1234567890.jpg',
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClassificationImageOperationDto)
  imageOperations?: ClassificationImageOperationDto[];

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  availableQuantity?: number;
}

/**
 * DTO cho phản hồi phân loại sản phẩm
 */
export class ClassificationResponseDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
  })
  type: string;

  @ApiProperty({
    description: 'Giá của phân loại',
    example: {
      value: 150000,
      listPrice: 200000,
      salePrice: 150000,
      currency: 'VND',
    },
    required: false,
  })
  price?: {
    value?: number;
    listPrice?: number;
    salePrice?: number;
    currency?: string;
  };

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [CustomFieldInputDto],
    required: false,
  })
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Danh sách ảnh của phân loại với URL CDN',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        key: {
          type: 'string',
          example: 'business/IMAGE/2025/01/15/classification-image.jpg',
        },
        position: { type: 'number', example: 0 },
        url: {
          type: 'string',
          example:
            'https://cdn.redai.vn/business/IMAGE/2025/01/15/classification-image.jpg',
        },
      },
    },
    example: [
      {
        key: 'business/IMAGE/2025/01/15/classification-image.jpg',
        position: 0,
        url: 'https://cdn.redai.vn/business/IMAGE/2025/01/15/classification-image.jpg',
      },
    ],
    required: false,
  })
  imagesMediaTypes?: Array<{
    key: string;
    position: number;
    url: string;
  }>;

  @ApiProperty({
    description:
      'Danh sách thao tác ảnh phân loại (tên field thay thế cho imagesMediaTypes để nhất quán với API chính)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        operation: { type: 'string', enum: ['ADD', 'DELETE'], example: 'ADD' },
        position: {
          type: 'number',
          example: 1,
          description: 'Vị trí ảnh cần xóa (cho DELETE)',
        },
        key: {
          type: 'string',
          example: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
          description: 'Khóa tệp (cho DELETE)',
        },
        mimeType: {
          type: 'string',
          example: 'image/jpeg',
          description: 'Loại MIME (cho ADD)',
        },
      },
    },
    example: [
      {
        operation: 'ADD',
        mimeType: 'image/jpeg',
      },
      {
        operation: 'DELETE',
        key: 'business/IMAGE/2025/01/15/old-shirt-image-1.jpg',
      },
    ],
    required: false,
  })
  imageOperations?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  // Field images đã được đổi thành imagesMediaTypes để nhất quán

  @ApiProperty({
    description: 'URLs upload cho ảnh phân loại',
    required: false,
    example: {
      classificationId: 87,
      imagesUploadUrls: [
        {
          url: 'https://redaivn.hn.ss.bfcplatform.vn/business/IMAGE/2025/06/classification-87-image-0-1748944837166',
          key: 'business/IMAGE/2025/06/classification-87-image-0-1748944837166',
          index: 0,
        },
      ],
    },
  })
  uploadUrls?: {
    classificationId: number;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  minQuantityPerPurchase?: number | null;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    required: false,
  })
  maxQuantityPerPurchase?: number | null;

  @ApiProperty({
    description: 'Mô tả phân loại',
    example: 'Mô tả chi tiết về phân loại này',
    required: false,
  })
  description?: string | null;

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'SKU-RED-001',
    required: false,
  })
  sku?: string | null;
}

export class UpdateClassificationImagesDto {
  @ApiProperty({
    description: 'Danh sách ảnh đã upload',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        key: {
          type: 'string',
          example:
            'business/IMAGE/2025/06/classification-87-image-0-1748944837166',
        },
        size: { type: 'number', example: 1024000 },
        position: { type: 'number', example: 0 },
      },
    },
    example: [
      {
        key: 'business/IMAGE/2025/06/classification-87-image-0-1748944837166',
        size: 1024000,
        position: 0,
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  images: Array<{
    key: string;
    size: number;
    position: number;
  }>;
}
