import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { UserProduct } from './user-product.entity';
import { HasPriceDto, StringPriceDto } from '../user/dto';
import {
  UserClassificationMetadata,
  ClassificationImagesMedia,
  ClassificationCustomFields,
} from '../types';


/**
 * Entity đại diện cho bảng user_classifications trong cơ sở dữ liệu
 * Bảng quản lý phân loại sản phẩm của người dùng
 */
@Entity('user_classifications')
export class UserClassification {
  /**
   * ID của phân loại
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Loại phân loại
   */
  @Column({ name: 'type', type: 'text', nullable: false, comment: 'Loại phân loại' })
  type: string;

  /**
   * <PERSON><PERSON><PERSON> của phân loại (dưới dạng JSON)
   */
  @Column({ name: 'price', type: 'jsonb', nullable: true, comment: 'Giá của phân loại (dưới dạng JSON)' })
  price: HasPriceDto | StringPriceDto;

  /**
   * ID sản phẩm
   */
  @Column({ name: 'product_id', type: 'integer', nullable: true, comment: 'ID sản phẩm' })
  productId: number;

  /**
   * Metadata chứa custom fields và thông tin bổ sung
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: false,
    default: () => "'{\"customFields\": []}'::jsonb", // Phù hợp với database schema
    comment: 'Metadata chứa custom fields và thông tin bổ sung',
  })
  metadata: UserClassificationMetadata;

  /**
   * Số lượng tối thiểu mỗi lần mua
   */
  @Column({
    name: 'min_quantity_per_purchase',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tối thiểu mỗi lần mua',
  })
  minQuantityPerPurchase: number | null;

  /**
   * Số lượng tối đa mỗi lần mua
   */
  @Column({
    name: 'max_quantity_per_purchase',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tối đa mỗi lần mua',
  })
  maxQuantityPerPurchase: number | null;

  /**
   * Mô tả phân loại
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả phân loại',
  })
  description: string | null;

  /**
   * Trường tùy chỉnh (jsonb)
   */
  @Column({
    name: 'custom_fields',
    type: 'jsonb',
    nullable: true,
    comment: 'Trường tùy chỉnh',
  })
  customFields: ClassificationCustomFields | null;

  /**
   * Thông tin media hình ảnh (jsonb)
   */
  @Column({
    name: 'images_media',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin media hình ảnh',
  })
  imagesMedia: ClassificationImagesMedia | null;

  /**
   * Mã SKU (Stock Keeping Unit) của phân loại
   */
  @Column({
    name: 'sku',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Mã SKU (Stock Keeping Unit) của phân loại',
  })
  sku: string | null;

  /**
   * Thời lượng dịch vụ (phút) - cho service packages
   */
  @Column({
    name: 'duration',
    type: 'integer',
    nullable: true,
    comment: 'Thời lượng dịch vụ (phút)',
  })
  duration: number | null;

  /**
   * Thời gian bắt đầu (timestamp) - cho service packages
   */
  @Column({
    name: 'start_time',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian bắt đầu (timestamp)',
  })
  startTime: number | null;

  /**
   * Thời gian kết thúc (timestamp) - cho service packages
   */
  @Column({
    name: 'end_time',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian kết thúc (timestamp)',
  })
  endTime: number | null;

  /**
   * Múi giờ - cho service packages
   */
  @Column({
    name: 'timezone',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Múi giờ',
  })
  timezone: string | null;

  /**
   * Trạng thái - cho service packages
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Trạng thái',
  })
  status: string | null;

  /**
   * Số lượng có sẵn - cho service packages
   */
  @Column({
    name: 'quantity',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng có sẵn',
  })
  quantity: number | null;
}
