# Test Script: Debug Inventory Issue

## Request để test

```bash
curl -X PUT "http://localhost:3000/api/business/user/products/347" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "inventory": [
      {
        "warehouseId": 22, 
        "operation": "ADD", 
        "availableQuantity": 12, 
        "sku": "sss", 
        "barcode": "ssssss"
      }
    ]
  }'
```

## Logs cần kiểm tra

### 1. Logs từ processAllInventoryOperations
```
🔄 BẮT ĐẦU xử lý TẤT CẢ 1 inventory operations cho sản phẩm 347
Không có DELETE operations cho inventory của sản phẩm 347 - bỏ qua xóa
➕✏️ Xử lý 1 ADD/UPDATE operations cho inventory
➕ TẠO INVENTORY MỚI với operation: ADD, warehouseId: 22, quantity: 12
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60, productId=347
🔍 XÁC NHẬN: Inventory ID=60 TỒN TẠI trong database
✅ HOÀN THÀNH xử lý TẤT CẢ inventory operations cho sản phẩm 347, tạo/cập nhật 1 inventory
🔍 KIỂM TRA CUỐI: Sản phẩm 347 có 1 inventory trong database
🔍 Danh sách inventory IDs: 60
⏳ Chờ 2 giây để kiểm tra inventory có bị xóa không...
🔍 KIỂM TRA SAU 2 GIÂY: Sản phẩm 347 có 1 inventory trong database
🔍 Danh sách inventory IDs sau delay: 60
```

### 2. Nếu inventory bị xóa
```
🚨 INVENTORY BỊ XÓA SAU KHI TẠO! Có process nào đó đang xóa inventory!
```

### 3. Logs từ orchestrator
```
Hoàn thành cập nhật sản phẩm ID: 347 (PHYSICAL)
```

## Kịch bản có thể

### Kịch bản 1: Transaction Rollback
- Inventory được tạo thành công
- Có error sau đó khiến transaction rollback
- Inventory bị xóa khỏi database

**Expected logs:**
```
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60
🔍 XÁC NHẬN: Inventory ID=60 TỒN TẠI trong database
[ERROR] Lỗi khi cập nhật sản phẩm ID: 347
🚨 INVENTORY BỊ XÓA SAU KHI TẠO!
```

### Kịch bản 2: Race Condition
- Inventory được tạo thành công
- Có process khác xóa inventory
- Timing issue

**Expected logs:**
```
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60
🔍 XÁC NHẬN: Inventory ID=60 TỒN TẠI trong database
🔍 KIỂM TRA CUỐI: Sản phẩm 347 có 1 inventory trong database
⏳ Chờ 2 giây để kiểm tra inventory có bị xóa không...
🚨 INVENTORY BỊ XÓA SAU KHI TẠO!
```

### Kịch bản 3: Database Issue
- Save thành công nhưng không commit
- Connection issue
- Database constraint

**Expected logs:**
```
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60
🚨 CẢNH BÁO: Inventory ID=60 KHÔNG TỒN TẠI trong database sau khi save!
```

## Cách debug

### 1. Chạy request và check logs
```bash
# Chạy request
curl -X PUT "http://localhost:3000/api/business/user/products/347" ...

# Check logs trong terminal
tail -f logs/app.log | grep -E "(inventory|INVENTORY|🔍|✅|🚨|❌)"
```

### 2. Check database trực tiếp
```sql
-- Trước khi chạy request
SELECT * FROM inventory WHERE product_id = 347;

-- Sau khi chạy request
SELECT * FROM inventory WHERE product_id = 347;

-- Check audit logs nếu có
SELECT * FROM audit_logs WHERE table_name = 'inventory' AND record_id = 60;
```

### 3. Check response
```json
{
  "code": 200,
  "message": "Cập nhật sản phẩm thành công",
  "result": {
    "id": "347",
    "inventory": [
      {
        "id": "60",  // ← Có ID này không?
        "productId": "347",
        "warehouseId": 22,
        "availableQuantity": 12
      }
    ]
  }
}
```

## Kết quả mong đợi

### ✅ Success Case
```
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60
🔍 XÁC NHẬN: Inventory ID=60 TỒN TẠI trong database
🔍 KIỂM TRA SAU 2 GIÂY: Sản phẩm 347 có 1 inventory trong database
Response: inventory với ID 60
Database: có record với ID 60
API get detail: trả về inventory
```

### ❌ Failure Case
```
🚨 INVENTORY BỊ XÓA SAU KHI TẠO! Có process nào đó đang xóa inventory!
Response: inventory với ID 60
Database: KHÔNG có record với ID 60
API get detail: KHÔNG có inventory
```

## Next Steps

Dựa vào logs, sẽ biết được:
1. Inventory có được tạo thành công không?
2. Có bị xóa sau khi tạo không?
3. Nguyên nhân: transaction rollback, race condition, hay database issue?
4. Cần fix ở đâu?
