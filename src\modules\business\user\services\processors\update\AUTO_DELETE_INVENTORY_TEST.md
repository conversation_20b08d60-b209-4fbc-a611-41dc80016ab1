# Test Auto Delete Inventory

## 🎯 **Tính Năng <PERSON>ới**

Backend **tự động phát hiện** inventory nào bị xóa bằng cách so sánh:
- **Database hiện có:** Inventory trong DB cho sản phẩm PHYSICAL
- **Request hiện tại:** Inventory trong request body
- **Kết quả:** Tự động xóa inventory không còn trong request

## 🧪 **Test Scenario**

### **Setup: Product PHYSICAL có 3 inventory**
```
Database hiện có:
- Inventory ID: 41 (Kho Đà Nẵng - warehouseId: 3)
- Inventory ID: 42 (<PERSON><PERSON> Minh - warehouseId: 7)
- Inventory ID: 43 (<PERSON><PERSON>ộ<PERSON> - warehouseId: 5)
```

### **Test Case 1: Xóa 1 inventory**

**Request:**
```json
{
  "name": "Updated Product",
  "inventory": [
    {
      "inventoryId": 41,
      "availableQuantity": 250,
      "sku": "SKU-001"
    },
    {
      "inventoryId": 42,
      "availableQuantity": 30,
      "sku": "SKU-002"
    }
    // Không có inventoryId: 43 → Sẽ bị xóa tự động
  ]
}
```

**Expected:**
- ✅ Update inventory ID 41, 42
- ✅ **Tự động xóa** inventory ID 43

### **Test Case 2: Xóa tất cả, thêm mới**

**Request:**
```json
{
  "name": "Updated Product",
  "inventory": [
    {
      "warehouseId": 8,
      "availableQuantity": 100,
      "sku": "NEW-SKU-001",
      "barcode": "NEW-BARCODE-001"
    }
    // Không có inventoryId nào → Tất cả inventory cũ sẽ bị xóa
  ]
}
```

**Expected:**
- ✅ **Tự động xóa** inventory ID 41, 42, 43
- ✅ Tạo inventory mới cho warehouseId: 8

### **Test Case 3: Sản phẩm không phải PHYSICAL**

**Request cho sản phẩm DIGITAL:**
```json
{
  "name": "Digital Product",
  "productType": "DIGITAL"
}
```

**Expected:**
- ✅ **Bỏ qua** xử lý inventory (chỉ xử lý cho PHYSICAL)

## 🔍 **Logic Flow**

```typescript
// Trong UpdateProductProcessor.processAutoDeleteInventory()
async processAutoDeleteInventory(productId, updateDto, userId) {
  // 1. Kiểm tra sản phẩm PHYSICAL
  const product = await this.userProductRepository.findById(productId);
  if (!product || product.productType !== 'PHYSICAL') {
    return; // Bỏ qua
  }

  // 2. Lấy inventory hiện có từ DB
  const existingInventoriesResult = await this.inventoryRepository.findAll({ productId });
  const existingIds = [41, 42, 43];

  // 3. Lấy IDs từ request
  const requestIds = [41, 42]; // Chỉ có ID 41, 42 trong request

  // 4. Tìm IDs cần xóa
  const idsToDelete = existingIds.filter(id => !requestIds.includes(id));
  // idsToDelete = [43]

  // 5. Xóa inventory
  await this.processInventoryDeletion(productId, idsToDelete, userId);
}
```

## ✅ **Expected Logs**

```
[UpdateProductProcessor] Sản phẩm 344 không phải PHYSICAL, bỏ qua xử lý inventory
[UpdateProductProcessor] Tự động phát hiện 1 inventory cần xóa: 43
[UpdateProductProcessor] Xóa 1 inventory cho sản phẩm 344
[UpdateProductProcessor] Đã xóa inventory 43
```

## 🎯 **Benefits**

### **1. Frontend Đơn Giản:**
```javascript
// Frontend chỉ cần gửi inventory còn lại
const updatedInventory = inventory.filter(inv => !inv.isDeleted);

const requestBody = {
  name: productName,
  inventory: updatedInventory
  // Không cần tính toán inventoriesToDelete
};
```

### **2. Automatic Cleanup:**
- ✅ Tự động xóa inventory không còn cần thiết
- ✅ Chỉ xử lý cho sản phẩm PHYSICAL
- ✅ Không cần frontend track deleted IDs

### **3. Consistent với Classifications:**
- ✅ Cùng pattern với auto-delete classifications
- ✅ Cùng error handling approach

## 🚨 **Edge Cases**

### **Case 1: Request inventory rỗng**
```json
{
  "name": "Product",
  "inventory": []
}
```
**Expected:** Xóa tất cả inventory hiện có

### **Case 2: Chỉ có inventory mới**
```json
{
  "name": "Product",
  "inventory": [
    {"warehouseId": 9, "availableQuantity": 50, "sku": "NEW"}
  ]
}
```
**Expected:** Xóa tất cả inventory cũ, tạo mới

### **Case 3: Error Handling**
- ✅ Nếu `findAll` thất bại → Log error, không cản trở update
- ✅ Nếu delete individual inventory thất bại → Log error, tiếp tục xóa các cái khác

## 🔧 **Implementation Status**

- ✅ Added `processAutoDeleteInventory()` method
- ✅ Added `processInventoryDeletion()` method  
- ✅ Added `InventoryRepository` injection
- ✅ Added to UpdateProductOrchestrator flow
- ✅ Only processes PHYSICAL products
- ✅ Graceful error handling

## 🎉 **Result**

Giờ đây khi frontend xóa inventory trong UI và gửi request, backend sẽ **tự động phát hiện và xóa** chúng mà không cần frontend tính toán `inventoriesToDelete`!

### **Test Command:**
```bash
curl -X PUT http://localhost:3000/v1/user/products/344 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Auto Delete Inventory",
    "inventory": [
      {
        "inventoryId": 41,
        "availableQuantity": 250
      }
    ]
  }'
```

**Expected:** Inventory ID 42, 43 sẽ bị xóa tự động! 🚀

## 📊 **Comparison**

### **Trước:**
- ❌ Frontend phải track deleted inventory IDs
- ❌ Phải gửi `inventoriesToDelete` array
- ❌ Dễ miss inventory cần xóa

### **Sau:**
- ✅ Frontend chỉ gửi inventory còn lại
- ✅ Backend tự động detect và xóa
- ✅ Consistent với classifications logic
