# Test Auto Delete Classifications

## 🎯 **Tính Năng <PERSON>ới**

Backend **tự động phát hiện** classifications nào bị xóa bằng cách so sánh:
- **Database hiện có:** Classifications trong DB
- **Request hiện tại:** Classifications trong request body
- **Kết quả:** Tự động xóa classifications không còn trong request

## 🧪 **Test Scenario**

### **Setup: Product có 3 classifications**
```
Database hiện có:
- Classification ID: 100 (Size S)
- Classification ID: 200 (Size M) 
- Classification ID: 300 (Size L)
```

### **Test Case 1: Xóa 1 classification**

**Request:**
```json
{
  "name": "Updated Product",
  "classifications": [
    {
      "id": 100,
      "type": "Size S",
      "price": {"listPrice": 20000, "salePrice": 15000, "currency": "VND"}
    },
    {
      "id": 200,
      "type": "Size M", 
      "price": {"listPrice": 25000, "salePrice": 20000, "currency": "VND"}
    }
    // Không có ID 300 → Sẽ bị xóa tự động
  ]
}
```

**Expected:**
- ✅ Update classification ID 100, 200
- ✅ **Tự động xóa** classification ID 300 + ảnh S3 của nó

### **Test Case 2: Xóa tất cả, thêm mới**

**Request:**
```json
{
  "name": "Updated Product",
  "classifications": [
    {
      "operation": "ADD",
      "type": "Size XL",
      "price": {"listPrice": 30000, "salePrice": 25000, "currency": "VND"}
    }
    // Không có ID 100, 200, 300 → Tất cả sẽ bị xóa
  ]
}
```

**Expected:**
- ✅ **Tự động xóa** classification ID 100, 200, 300 + ảnh S3
- ✅ Tạo classification mới Size XL

### **Test Case 3: Kết hợp với explicit delete**

**Request:**
```json
{
  "name": "Updated Product", 
  "classifications": [
    {
      "id": 100,
      "type": "Size S Updated"
    }
    // Không có ID 200 → Tự động xóa
  ],
  "classificationsToDelete": [300] // Explicit delete
}
```

**Expected:**
- ✅ Update classification ID 100
- ✅ **Tự động xóa** classification ID 200
- ✅ **Explicit xóa** classification ID 300
- ✅ Tổng cộng xóa ID 200, 300

## 🔍 **Logic Flow**

```typescript
// 1. Lấy classifications hiện có từ DB
const existingClassifications = await this.classificationService.getByProductId(productId);
const existingIds = [100, 200, 300];

// 2. Lấy IDs từ request
const requestIds = [100]; // Chỉ có ID 100 trong request

// 3. Tìm IDs cần xóa
const idsToDelete = existingIds.filter(id => !requestIds.includes(id));
// idsToDelete = [200, 300]

// 4. Kết hợp với explicit delete
const explicitDeleteIds = updateDto.classificationsToDelete || [];
const allIdsToDelete = [...new Set([...idsToDelete, ...explicitDeleteIds])];

// 5. Xóa tất cả
await this.processClassificationsDeletion(productId, allIdsToDelete, userId);
```

## ✅ **Expected Logs**

```
[UpdateProductProcessor] Tự động phát hiện 2 classifications cần xóa: 200, 300
[UpdateProductProcessor] Explicit delete: 0 classifications: 
[UpdateProductProcessor] Tổng cộng xóa 2 classifications: 200, 300
[UpdateProductProcessor] Xóa 2 classifications cho sản phẩm 344
[ClassificationService] Xóa 2 ảnh S3 cho classification 200
[ClassificationService] Xóa 1 ảnh S3 cho classification 300
```

## 🎯 **Benefits**

### **1. Frontend Đơn Giản:**
```javascript
// Frontend chỉ cần gửi classifications còn lại
const updatedClassifications = classifications.filter(c => !c.isDeleted);

const requestBody = {
  name: productName,
  classifications: updatedClassifications
  // Không cần tính toán classificationsToDelete
};
```

### **2. Automatic Cleanup:**
- ✅ Tự động xóa classifications không còn cần thiết
- ✅ Tự động xóa ảnh S3 của classifications bị xóa
- ✅ Không cần frontend track deleted IDs

### **3. Backward Compatible:**
- ✅ Vẫn hỗ trợ `classificationsToDelete` nếu cần
- ✅ Logic cũ vẫn hoạt động bình thường

## 🚨 **Edge Cases**

### **Case 1: Request rỗng**
```json
{
  "name": "Product",
  "classifications": []
}
```
**Expected:** Xóa tất cả classifications hiện có

### **Case 2: Chỉ có classifications mới**
```json
{
  "name": "Product",
  "classifications": [
    {"operation": "ADD", "type": "New Size"}
  ]
}
```
**Expected:** Xóa tất cả classifications cũ, tạo mới

### **Case 3: Error Handling**
- ✅ Nếu `getByProductId` thất bại → Log error, không cản trở update
- ✅ Nếu delete individual classification thất bại → Log error, tiếp tục xóa các cái khác

## 🎉 **Result**

Giờ đây khi frontend xóa classifications trong UI và gửi request, backend sẽ **tự động phát hiện và xóa** chúng mà không cần frontend tính toán `classificationsToDelete`! 🚀
