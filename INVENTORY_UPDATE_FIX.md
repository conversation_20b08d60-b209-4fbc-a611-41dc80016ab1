# Fix: Inventory bị xóa sau khi update sản phẩm

## Vấn đề

Khi update sản phẩm PHYSICAL và thêm inventory mới:
1. API trả về inventory với ID (ví dụ: "58")
2. Nhưng kiểm tra database thì không có
3. API get detail cũng không thấy inventory

## Nguyên nhân

Vấn đề nằm ở logic **tự động xóa inventory** trong `processAutoDeleteInventory`:

### Logic lỗi cũ:

```typescript
// Lấy IDs từ request
const requestIds = (updateDto.inventory || [])
  .map(inv => inv.inventoryId)  // ← inventoryId = undefined cho inventory mới
  .filter(id => id !== undefined) as number[];

// requestIds = [] (rỗng vì inventory mới chưa có ID)
// existingIds = [58] (inventory vừa tạo)
// idsToDelete = [58] (inventory vừa tạo bị đánh dấu để xóa)
```

## Giải pháp

Thay vì thay đổi thứ tự, **cải thiện logic để hỗ trợ `operation` field từ frontend**, giống như `imageOperations` và `classifications`:

### Giải pháp mới: Inventory Operations

Frontend gửi `operation` field để kiểm soát rõ ràng:

```json
{
  "inventory": [
    {
      "operation": "DELETE",
      "inventoryId": 123
    },
    {
      "operation": "UPDATE",
      "inventoryId": 124,
      "warehouseId": 1,
      "availableQuantity": 150,
      "sku": "SHIRT-001-UPDATED"
    },
    {
      "operation": "ADD",
      "warehouseId": 2,
      "availableQuantity": 50,
      "sku": "SHIRT-002-NEW"
    }
  ]
}
```

## Code thay đổi

### 1. Cập nhật ProductInventoryDto

**File**: `src/modules/business/user/dto/product-inventory.dto.ts`

```typescript
@IsIn(['ADD', 'UPDATE', 'DELETE'], {
  message: 'Operation chỉ chấp nhận giá trị ADD, UPDATE hoặc DELETE'
})
operation?: 'ADD' | 'UPDATE' | 'DELETE';
```

### 2. Thêm logic xử lý operations

**File**: `src/modules/business/user/services/processors/update/update-product.processor.ts`

```typescript
async processInventoryOperations(
  productId: number,
  updateDto: BusinessUpdateProductDto,
  userId: number,
): Promise<void> {
  // Xử lý các thao tác DELETE trước
  const deleteOperations = updateDto.inventory.filter(inv => inv.operation === 'DELETE');
  if (deleteOperations.length > 0) {
    const idsToDelete = deleteOperations
      .map(inv => inv.inventoryId)
      .filter(id => id !== undefined) as number[];

    await this.processInventoryDeletion(productId, idsToDelete, userId);
  }
}
```

## Kết quả

### Ưu điểm của giải pháp mới:

1. ✅ **Frontend kiểm soát rõ ràng**: Không còn logic "đoán" inventory nào cần xóa
2. ✅ **Tương thích ngược**: Logic cũ vẫn hoạt động nếu không có `operation` field
3. ✅ **Nhất quán**: Giống pattern của `imageOperations` và `classifications`
4. ✅ **An toàn**: Không xóa nhầm inventory vừa tạo

### Cách sử dụng:

```json
// Xóa inventory cũ và thêm mới
{
  "inventory": [
    {"operation": "DELETE", "inventoryId": 123},
    {"operation": "ADD", "warehouseId": 1, "availableQuantity": 100}
  ]
}

// Chỉ cập nhật inventory hiện có
{
  "inventory": [
    {"operation": "UPDATE", "inventoryId": 124, "availableQuantity": 200}
  ]
}

// Tương thích ngược (không có operation field)
{
  "inventory": [
    {"inventoryId": 124, "availableQuantity": 200}
  ]
}
```

## Test case

### Trước khi sửa:
```json
// Request: Thêm inventory mới
{"inventory": [{"warehouseId": 1, "availableQuantity": 100}]}

// API response: Trả về ID 58
"inventory": [{"id": "58", ...}]

// Database: KHÔNG CÓ ID 58 (bị xóa bởi processAutoDeleteInventory)
// API get detail: KHÔNG CÓ inventory
```

### Sau khi sửa:
```json
// Request: Thêm inventory mới với operation
{"inventory": [{"operation": "ADD", "warehouseId": 1, "availableQuantity": 100}]}

// API response: Trả về ID 58
"inventory": [{"id": "58", ...}]

// Database: CÓ ID 58 (không bị xóa nhầm)
// API get detail: CÓ inventory với ID 58
```

## Lưu ý

- Logic `processAutoDeleteInventory` được cải thiện để kiểm tra `operation` field trước
- Nếu có `operation` field, sử dụng logic mới `processInventoryOperations`
- Nếu không có `operation` field, sử dụng logic cũ để tương thích ngược
- DELETE operations được xử lý trước ADD/UPDATE operations
