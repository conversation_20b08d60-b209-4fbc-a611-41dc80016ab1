# Logic Inventory Operations - <PERSON><PERSON> Ràng và Đơn Giản

## Nguyên tắc

**CHỈ XÓA KHI CÓ `operation: "DELETE"`** - Không có logic tự động đoán nào cả!

## Flow mới

### BƯỚC 6: Xử lý ADD/UPDATE Operations
**File**: `PhysicalProductUpdateProcessor.updateSingleInventory`

```typescript
// Logic rõ ràng dựa trên operation field
const operation = inventoryWithOperation.operation;

// Bỏ qua DELETE operations (được xử lý ở bước khác)
if (operation === 'DELETE') {
  return null; // Không xử lý DELETE ở đây
}

// Quyết định tạo mới hay cập nhật
const shouldCreateNew = operation === 'ADD' || !inventoryData.inventoryId;

if (shouldCreateNew) {
  // ➕ TẠO INVENTORY MỚI
} else {
  // ✏️ CẬP NHẬT INVENTORY HIỆN CÓ
}
```

### BƯỚC 9: Xử lý DELETE Operations
**File**: `UpdateProductProcessor.processInventoryDeleteOperations`

```typescript
// CHỈ xử lý các thao tác có operation: "DELETE"
const deleteOperations = updateDto.inventory.filter(inv => inv.operation === 'DELETE');

if (deleteOperations.length === 0) {
  return; // Không có gì để xóa
}

// 🗑️ XÓA INVENTORY theo yêu cầu
await this.processInventoryDeletion(productId, idsToDelete, userId);
```

## Các trường hợp sử dụng

### 1. Thêm inventory mới
```json
{
  "inventory": [
    {
      "operation": "ADD",
      "warehouseId": 22,
      "availableQuantity": 12,
      "sku": "sss",
      "barcode": "ssssss"
    }
  ]
}
```
**Kết quả**: ➕ Tạo inventory mới, KHÔNG xóa gì cả

### 2. Cập nhật inventory hiện có
```json
{
  "inventory": [
    {
      "operation": "UPDATE",
      "inventoryId": 123,
      "availableQuantity": 50
    }
  ]
}
```
**Kết quả**: ✏️ Cập nhật inventory ID 123, KHÔNG xóa gì cả

### 3. Xóa inventory
```json
{
  "inventory": [
    {
      "operation": "DELETE",
      "inventoryId": 123
    }
  ]
}
```
**Kết quả**: 🗑️ Xóa inventory ID 123

### 4. Thêm mới + Xóa cũ
```json
{
  "inventory": [
    {
      "operation": "DELETE",
      "inventoryId": 123
    },
    {
      "operation": "ADD",
      "warehouseId": 22,
      "availableQuantity": 12
    }
  ]
}
```
**Kết quả**: 🗑️ Xóa ID 123, ➕ Tạo inventory mới

### 5. Không có operation (tương thích ngược)
```json
{
  "inventory": [
    {
      "warehouseId": 22,
      "availableQuantity": 12
    }
  ]
}
```
**Kết quả**: ➕ Tạo inventory mới (logic cũ), có thể xóa inventory cũ (logic auto-delete)

## Debug Logs

### Logs thành công:
```
➕ TẠO INVENTORY MỚI với operation: ADD, warehouseId: 22, quantity: 12
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60, productId=347
Không có DELETE operations cho inventory của sản phẩm 347 - bỏ qua xóa
✅ Hoàn thành xử lý inventory DELETE operations cho sản phẩm 347
```

### Logs xóa:
```
🗑️ Xử lý 1 DELETE operations cho inventory: 123
❌ ĐÃ XÓA INVENTORY KHỎI DATABASE: ID=123
✅ Hoàn thành xử lý inventory DELETE operations cho sản phẩm 347
```

## Ưu điểm

1. ✅ **Rõ ràng**: Chỉ xóa khi có `operation: "DELETE"`
2. ✅ **An toàn**: Không xóa nhầm inventory vừa tạo
3. ✅ **Tương thích ngược**: Logic cũ vẫn hoạt động
4. ✅ **Dễ debug**: Logs rõ ràng cho từng operation
5. ✅ **Nhất quán**: Giống pattern của `imageOperations`

## Test Case

**Request**:
```json
{
  "inventory": [
    {
      "operation": "ADD",
      "warehouseId": 22,
      "availableQuantity": 12,
      "sku": "sss",
      "barcode": "ssssss"
    }
  ]
}
```

**Expected Result**:
- ✅ Inventory được tạo với ID mới
- ✅ Database có record mới
- ✅ API get detail trả về inventory
- ✅ KHÔNG có inventory nào bị xóa
