# Test Classification Operation Field

## 🎯 **T<PERSON>h Năng Mới**

Thêm field `operation` vào `UpdateClassificationDto` để phân biệt:
- **`operation: "ADD"`** → Tạo classification mới (dù có ID)
- **`operation: "UPDATE"`** → Cập nhật classification hiện có
- **Không có `operation`** → Logic cũ (có ID = update, không ID = create)

## 🧪 **Test Cases**

### **Case 1: Tạo Classification Mới với operation = "ADD"**

```json
{
  "name": "Test Product",
  "classifications": [
    {
      "operation": "ADD",
      "type": "Màu sắc mới",
      "description": "Biến thể mới được thêm",
      "price": {
        "listPrice": 25000,
        "salePrice": 20000,
        "currency": "VND"
      },
      "imageOperations": [
        {
          "operation": "ADD",
          "mimeType": "image/png"
        }
      ]
    }
  ]
}
```

**Expected:** Tạo classification mới, bỏ qua ID nếu có

### **Case 2: Cập nhật Classification với operation = "UPDATE"**

```json
{
  "name": "Test Product",
  "classifications": [
    {
      "id": 192,
      "operation": "UPDATE",
      "type": "Màu sắc đã cập nhật",
      "price": {
        "listPrice": 30000,
        "salePrice": 25000,
        "currency": "VND"
      },
      "imageOperations": [
        {
          "operation": "DELETE",
          "key": "business/IMAGE/2025/06/old-image.jpg"
        },
        {
          "operation": "ADD",
          "mimeType": "image/jpeg"
        }
      ]
    }
  ]
}
```

**Expected:** Cập nhật classification ID 192

### **Case 3: Logic Cũ (Backward Compatibility)**

```json
{
  "name": "Test Product",
  "classifications": [
    {
      "id": 192,
      "type": "Màu sắc cũ",
      "price": {
        "listPrice": 20000,
        "salePrice": 15000,
        "currency": "VND"
      }
    },
    {
      "type": "Màu sắc mới không có ID",
      "price": {
        "listPrice": 18000,
        "salePrice": 15000,
        "currency": "VND"
      }
    }
  ]
}
```

**Expected:** 
- Classification có ID → Update
- Classification không ID → Create

### **Case 4: Tạo Mới Dù Có ID (operation = "ADD")**

```json
{
  "name": "Test Product",
  "classifications": [
    {
      "id": 999999,
      "operation": "ADD",
      "type": "Biến thể mới dù có ID",
      "description": "ID sẽ bị bỏ qua vì operation = ADD",
      "price": {
        "listPrice": 22000,
        "salePrice": 18000,
        "currency": "VND"
      }
    }
  ]
}
```

**Expected:** Tạo classification mới, ID 999999 bị bỏ qua

## 🔍 **Logic Flow**

```typescript
// Trong UpdateProductProcessor.processClassificationsUpdate()
for (const classificationDto of updateDto.classifications) {
  const shouldCreateNew = classificationDto.operation === 'ADD' || !classificationDto.id;
  
  if (shouldCreateNew) {
    // Tạo mới - bỏ qua ID nếu có
    const newClassification = await this.classificationService.create(
      productId,
      createClassificationDto,
      userId,
    );
  } else {
    // Cập nhật - cần có ID
    const updatedClassification = await this.classificationService.update(
      classificationDto.id!,
      classificationDto,
      userId,
    );
  }
}
```

## ✅ **Expected Logs**

```
[UpdateProductProcessor] Tạo classification mới với operation: ADD, id: undefined
[UpdateProductProcessor] Tạo classification mới với operation: ADD, id: 999999
[UpdateProductProcessor] Cập nhật classification hiện có với ID: 192
```

## 🎯 **Use Cases**

### **Frontend Workflow:**

1. **Thêm biến thể mới:**
   ```json
   {"operation": "ADD", "type": "Size XL", ...}
   ```

2. **Sửa biến thể có sẵn:**
   ```json
   {"id": 123, "operation": "UPDATE", "type": "Size L (updated)", ...}
   ```

3. **Duplicate biến thể:**
   ```json
   {"id": 123, "operation": "ADD", "type": "Size L (copy)", ...}
   ```

## 🚨 **Error Cases**

### **Case 1: UPDATE không có ID**
```json
{
  "operation": "UPDATE",
  "type": "Test"
}
```
**Expected Error:** Classification service sẽ báo lỗi khi update với ID undefined

### **Case 2: Invalid Operation**
```json
{
  "operation": "INVALID",
  "type": "Test"
}
```
**Expected Error:** Validation error - Operation chỉ chấp nhận ADD hoặc UPDATE

## 🔧 **Implementation Status**

- ✅ Added `operation` field to `UpdateClassificationDto`
- ✅ Updated validation with `@IsIn(['ADD', 'UPDATE'])`
- ✅ Updated logic in `UpdateProductProcessor.processClassificationsUpdate()`
- ✅ Added logging for debugging
- ✅ Backward compatibility maintained

## 🎉 **Benefits**

1. **Explicit Control:** Frontend có thể chỉ định chính xác muốn tạo mới hay cập nhật
2. **Duplicate Feature:** Có thể duplicate classification bằng cách dùng ID + operation="ADD"
3. **Backward Compatible:** Code cũ vẫn hoạt động bình thường
4. **Clear Intent:** Code rõ ràng hơn về ý định của từng thao tác
