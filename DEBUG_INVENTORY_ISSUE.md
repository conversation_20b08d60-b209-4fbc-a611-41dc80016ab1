# Debug: Inventory bị xóa sau khi tạo

## Vấn đề

Request:
```json
{
  "inventory": [
    {
      "warehouseId": 22, 
      "operation": "ADD", 
      "availableQuantity": 12, 
      "sku": "sss", 
      "barcode": "ssssss"
    }
  ]
}
```

- ✅ API response trả về inventory với ID 60
- ❌ Database không có ID 60
- ❌ API get detail không thấy inventory

## Debug logs cần kiểm tra

### 1. Logs từ processAllInventoryOperations
```
🔄 BẮT ĐẦU xử lý TẤT CẢ 1 inventory operations cho sản phẩm 347
Không có DELETE operations cho inventory của sản phẩm 347 - bỏ qua xóa
➕✏️ Xử lý 1 ADD/UPDATE operations cho inventory
➕ TẠO INVENTORY MỚI với operation: ADD, warehouseId: 22, quantity: 12
✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=60, productId=347
🔍 XÁC NHẬN: Inventory ID=60 TỒN TẠI trong database
✅ HOÀN THÀNH xử lý TẤT CẢ inventory operations cho sản phẩm 347, tạo/cập nhật 1 inventory
🔍 KIỂM TRA CUỐI: Sản phẩm 347 có 1 inventory trong database
🔍 Danh sách inventory IDs: 60
```

### 2. Logs từ orchestrator
```
Hoàn thành cập nhật sản phẩm ID: 347 (PHYSICAL)
```

### 3. Logs từ buildUpdateResponse
```
// Không có logs đặc biệt
```

## Nguyên nhân có thể

### 1. Transaction Rollback
- Có error sau khi tạo inventory
- Transaction bị rollback
- Inventory bị xóa khỏi database

### 2. Async Race Condition
- Có process khác xóa inventory
- Timing issue

### 3. Database Connection Issue
- Connection bị đóng
- Data không được commit

### 4. Hidden Error
- Error bị catch và không throw
- Silent failure

## Cách debug

### 1. Kiểm tra logs
Tìm trong logs:
- `🚨 CẢNH BÁO: Inventory ID=60 KHÔNG TỒN TẠI`
- `🔍 KIỂM TRA CUỐI: Sản phẩm 347 có 0 inventory`
- Bất kỳ error nào sau khi tạo inventory

### 2. Kiểm tra transaction
- Có `@Transactional()` decorator nào không?
- Có error nào khiến rollback không?

### 3. Kiểm tra database
```sql
-- Kiểm tra inventory table
SELECT * FROM inventory WHERE product_id = 347;

-- Kiểm tra logs/audit table nếu có
SELECT * FROM audit_logs WHERE table_name = 'inventory' AND record_id = 60;
```

### 4. Test đơn giản
Thử request không có operation:
```json
{
  "inventory": [
    {
      "warehouseId": 22,
      "availableQuantity": 12,
      "sku": "sss",
      "barcode": "ssssss"
    }
  ]
}
```

## Giải pháp tạm thời

### 1. Tắt transaction
Comment out `@Transactional()` decorator nếu có

### 2. Thêm delay
```typescript
inventory = await this.inventoryRepository.save(newInventory);
await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s
const verifyInventory = await this.inventoryRepository.findOne({ where: { id: inventory.id } });
```

### 3. Kiểm tra error handling
Đảm bảo không có try-catch nào nuốt error

## Kết quả mong đợi

Sau khi debug, sẽ tìm ra:
- Inventory có được tạo thành công không?
- Có bị xóa ở đâu không?
- Error gì khiến rollback?
- Timing issue nào?
