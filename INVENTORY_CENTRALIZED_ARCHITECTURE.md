# Architecture: Tậ<PERSON> trung tất cả Inventory Operations ở BƯỚC 9

## Nguy<PERSON><PERSON> tắc

**TẤT CẢ inventory operations (ADD/UPDATE/DELETE) được xử lý tại MỘT ĐIỂM DUY NHẤT: BƯỚC 9**

## Architecture mới

### BƯỚC 6: PhysicalProductUpdateProcessor
- ❌ **KHÔNG** xử lý inventory
- ✅ Chỉ xử lý: shipment config, validation
- ✅ Return `inventory: []` (empty)

### BƯỚC 9: UpdateProductProcessor.processAllInventoryOperations
- ✅ Xử lý **TẤT CẢ** inventory operations
- ✅ DELETE operations trước
- ✅ ADD/UPDATE operations sau
- ✅ Return `InventoryResponseDto[]`

## Flow chi tiết

```typescript
// BƯỚC 6: PhysicalProductUpdateProcessor.updatePhysicalProduct
async updatePhysicalProduct(product, updateDto, userId) {
  // BƯỚC 1: Validate
  await this.validatePhysicalProductData(updateDto);
  
  // BƯỚC 2: Cập nhật shipment config
  this.updateShipmentConfig(product, updateDto);
  
  // BƯỚC 3: Bỏ qua inventory (sẽ xử lý ở BƯỚC 9)
  
  // BƯỚC 4: <PERSON><PERSON><PERSON> sản phẩm
  const updatedProduct = await this.userProductRepository.save(product);
  
  return {
    product: updatedProduct,
    inventory: [], // ← EMPTY, không xử lý inventory
  };
}

// BƯỚC 9: UpdateProductProcessor.processAllInventoryOperations
async processAllInventoryOperations(productId, updateDto, userId) {
  // BƯỚC 1: Xử lý DELETE operations
  await this.processInventoryDeleteOperations(productId, updateDto, userId);
  
  // BƯỚC 2: Xử lý ADD/UPDATE operations  
  const inventoryResults = await this.processInventoryAddUpdateOperations(productId, updateDto, userId);
  
  return inventoryResults; // ← Trả về inventory đã xử lý
}
```

## Ưu điểm

### ✅ Separation of Concerns
- **PhysicalProductUpdateProcessor**: Chỉ lo product-specific logic (shipment, validation)
- **UpdateProductProcessor**: Lo inventory operations (ADD/UPDATE/DELETE)

### ✅ Centralized Logic
- Tất cả inventory operations ở một nơi
- Dễ debug, dễ maintain
- Logic rõ ràng, không phân tán

### ✅ Consistent Flow
- DELETE → ADD/UPDATE (thứ tự đúng)
- Không có race condition
- Không có logic trùng lặp

## Request/Response

### Request
```json
{
  "inventory": [
    {
      "operation": "DELETE",
      "inventoryId": 123
    },
    {
      "operation": "ADD",
      "warehouseId": 22,
      "availableQuantity": 12,
      "sku": "sss",
      "barcode": "ssssss"
    }
  ]
}
```

### Flow xử lý
```
BƯỚC 6: PhysicalProductUpdateProcessor
  ↓ Bỏ qua inventory, chỉ xử lý shipment config
  ↓ Return inventory: []

BƯỚC 9: processAllInventoryOperations
  ↓ 🗑️ DELETE inventory ID 123
  ↓ ➕ ADD inventory mới (warehouseId: 22, quantity: 12)
  ↓ Return [{ id: 124, warehouseId: 22, ... }]

BƯỚC 10: buildUpdateResponse
  ↓ Sử dụng inventory từ BƯỚC 9
  ↓ Return response với inventory mới
```

### Response
```json
{
  "code": 200,
  "message": "Cập nhật sản phẩm thành công",
  "result": {
    "id": "347",
    "inventory": [
      {
        "id": "124",
        "productId": "347",
        "warehouseId": 22,
        "availableQuantity": 12,
        "sku": "sss",
        "barcode": "ssssss"
      }
    ]
  }
}
```

## Debug Logs

```
BƯỚC 6: PhysicalProductUpdateProcessor
  → Updating PHYSICAL product: Xin (ID: 347)
  → Bỏ qua inventory update (sẽ được xử lý ở BƯỚC 9)

BƯỚC 9: processAllInventoryOperations  
  → 🔄 BẮT ĐẦU xử lý TẤT CẢ 2 inventory operations cho sản phẩm 347
  → 🗑️ Xử lý 1 DELETE operations cho inventory: 123
  → ❌ ĐÃ XÓA INVENTORY KHỎI DATABASE: ID=123
  → ➕✏️ Xử lý 1 ADD/UPDATE operations cho inventory
  → ➕ TẠO INVENTORY MỚI với operation: ADD, warehouseId: 22, quantity: 12
  → ✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=124, productId=347
  → ✅ HOÀN THÀNH xử lý TẤT CẢ inventory operations cho sản phẩm 347, tạo/cập nhật 1 inventory
```

## Kết quả

- ✅ **Không có logic trùng lặp**
- ✅ **Inventory operations tập trung ở một nơi**
- ✅ **PhysicalProductUpdateProcessor chỉ lo product logic**
- ✅ **Thứ tự xử lý đúng: DELETE → ADD/UPDATE**
- ✅ **Dễ debug và maintain**
